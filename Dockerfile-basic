# 基础镜像
FROM inngke-docker.pkg.coding.net/java/inngke-docker-hub/python:3.13-slim-bookworm AS builder

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install -r requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple

# 第二阶段：运行环境
FROM inngke-docker.pkg.coding.net/java/inngke-docker-hub/python:3.13-slim-bookworm

    # 替换为国内源
RUN echo "deb http://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main contrib non-free non-free-firmware\n\
deb http://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main contrib non-free non-free-firmware\n\
deb http://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-backports main contrib non-free non-free-firmware\n\
deb http://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware" \
    > /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y libgl1 libglib2.0-0 \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制 Python 包
COPY --from=builder /usr/local/lib/python3.13/site-packages/ /usr/local/lib/python3.13/site-packages/
COPY --from=builder /usr/local/bin/ /usr/local/bin/