import logging
import time

import requests


logger = logging.getLogger(__name__)


class JinaApi:
    API_URL = 'https://api.jina.ai/v1/embeddings'
    API_KEY = 'jina_b706fb208b434855b4a86ad98a3c895alrTwSxp_t4jHjIFzU1D8te6VOe52'

    @classmethod
    def get_vector(cls, image=None, text=None, key=None):
        start_time = time.time()
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {cls.API_KEY}'
        }
        data = {
            "model": "jina-embeddings-v4",
            "dimensions": 1024,
            "task": "text-matching",
            "input": [
                {
                    "image": image
                }
            ]
        }
        response = requests.post(cls.API_URL, json=data, headers=headers)
        data = response.json().get('data', [])

        logger.info(f'JinaApi get_vector cost {time.time() - start_time} seconds')
        return (key, data[0].get('embedding')) if len(data) > 0 else (key, None)

    @classmethod
    def get_vectors(cls, images=None):
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {cls.API_KEY}'
        }
        data = {
            "model": "jina-embeddings-v4",
            "dimensions": 1024,
            "task": "text-matching",
            "input": [
                { "image": image } for image in images
            ]
        }
        response = requests.post(cls.API_URL, json=data, headers=headers)
        data = response.json().get('data', [])

        return [item.get('embedding') for item in data]


jina_api = JinaApi()

# pip install transformers torch peft torchvision pillow -i https://pypi.t^Ca.tsinghua.edu.cn/simple