import json
import logging

from core.exception.ServiceException import ServiceException
from core.func import get_user_identity
from sdk.dify import WorkflowClient
from flask import has_app_context

logger = logging.getLogger(__name__)


class SubtitleMatchApp:

    def __init__(self):
        self.client = WorkflowClient(api_key='app-4G3V6Lx9HJVlg8GGewfARWEX', base_url='http://170.106.197.97/v1')

    def run(self, inputs: dict[str, str], user_id=None):

        # 如果在flask上下文环境中，获取用户id
        if user_id is None and has_app_context():
            user_id, _ = get_user_identity()
        response = self.client.run(inputs=inputs, response_mode='blocking', user=user_id)

        response.raise_for_status()

        body = response.json()
        result = body.get('data', {}).get('outputs', {}).get('result', None)
        if result is None:
            logger.info(f'dify应用调用失败，返回值：{response.text}')
            raise ServiceException(f'dify应用调用失败')
        return json.loads(result)

    def run_for_retry(self, inputs: dict[str, str], user_id, retry_times: int = 3):
        for _ in range(retry_times):
            try:
                return self.run(inputs=inputs, user_id=user_id)
            except Exception as _:
                continue

        raise ServiceException(f'dify应用调用失败，重试{retry_times}次失败')


subtitle_match_app = SubtitleMatchApp()
