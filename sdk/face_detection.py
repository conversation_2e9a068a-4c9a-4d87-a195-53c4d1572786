import json
import os

import requests

import logging

logger = logging.getLogger(__name__)


class FaceDetection:
    YOLO_FACE_DETECTION = os.environ.get('YOLO_FACE_DETECTION', 'http://47.120.49.111:22839')

    @classmethod
    def detection(cls, url, times):
        logger.info(f'人脸检测 request-body : {json.dumps({'url': url, 'ts': times})}')

        response = requests.get(f'{cls.YOLO_FACE_DETECTION}/api/video/face', params={
            'url': url,
            'ts': times
        })
        logger.info(f'人脸检测 response-body : {response.text}')

        return response.json().get('data') if response.json().get('data') else []


face_detection = FaceDetection()
