import os

import requests

from core.exception.ServiceException import ServiceException


class ClipAiYk:

    def __init__(self):
        self._endpoint = os.environ.get('CLIP_AI_YK_SERVER')
        if not self._endpoint:
            raise ServiceException('未找到配置:CLIP_AI_YK_SERVER')

    def embed(self, image=None, text=None):
        url = f'{self._endpoint}/api/clip/eval'

        files, params = None, None

        if image is not None:
            files = {'image': open(image, 'rb')}
        elif text is not None:
            params = {'text': text}
        else:
            return None

        response = requests.post(url, params=params, files=files)

        return response.json().get('data')

    def embed_images(self, images: list[str]):
        return {
            image: self.embed(image=image)
            for image in images
        }


clip_ai_yk = ClipAiYk()
