import logging
import time
from typing import Optional

import requests

logger = logging.getLogger(__name__)


class VoiceAta:
    _ENDPOINT = 'https://openspeech.bytedance.com'

    def __init__(self, appid: str, access_token: str):
        """
        初始化客户端
        
        Args:
            appid: 应用标识
            access_token: 访问令牌
        """
        self._appid = appid
        self._access_token = access_token
        self._headers = {
            'Authorization': f'Bearer; {access_token}',
        }

    def _submit_task(self,
                     audio_url: Optional[str] = None,
                     audio_data: Optional[bytes] = None,
                     words_per_line: int = 64,
                     max_lines: int = 1,
                     use_itn: bool = False,
                     language: str = 'zh-CN',
                     caption_type: str = 'speech',
                     use_punc: bool = True,
                     use_ddc: bool = True,
                     with_speaker_info: bool = True) -> dict[str, any]:
        """
        提交音视频字幕生成任务
        
        Args:
            audio_url: 音频文件URL
            audio_data: 音频文件二进制数据
            language: 字幕语言类型，默认中文
            words_per_line: 每行最多展示字数，默认46
            max_lines: 每屏最多展示行数，默认1
            use_itn: 是否使用数字转换功能，默认False
            use_punc: 是否增加标点，默认False
            caption_type: 字幕识别类型(auto/speech/singing)，默认auto
            with_speaker_info: 是否返回说话人信息，默认False
            
        Returns:
            Dict包含任务ID等信息
        """
        if not audio_url and not audio_data:
            raise ValueError("必须提供 audio_url 或 audio_data 其中之一")
        if audio_url and audio_data:
            raise ValueError("audio_url 和 audio_data 不能同时提供")

        params = {
            'appid': self._appid,
            'words_per_line': words_per_line,
            'max_lines': max_lines,
            'use_itn': str(use_itn),
            'language': language,
            'caption_type': caption_type,
            'use_punc': str(use_punc),
            'use_ddc': use_ddc,
            'with_speaker_info': str(with_speaker_info)
        }

        if audio_url:
            response = requests.post(
                f'{self._ENDPOINT}/api/v1/vc/submit',
                params=params,
                json={'url': audio_url},
                headers=self._headers
            )
        else:
            # 二进制上传时使用 audio/mp3 content-type
            headers = self._headers.copy()
            headers['content-type'] = 'audio/*'
            response = requests.post(
                f'{self._ENDPOINT}/api/v1/vc/submit',
                params=params,
                data=audio_data,
                headers=headers,
            )

        if response.status_code != 200:
            raise Exception(f"提交任务失败: {response.text}")

        return response.json()

    def _query_result(self, task_id: str, blocking: bool = True) -> dict[str, any]:
        """
        查询字幕生成结果
        
        Args:
            task_id: 任务ID
            blocking: 是否阻塞等待结果，默认True
            
        Returns:
            Dict包含识别结果等信息
        """
        params = {
            'appid': self._appid,
            'id': task_id,
            'blocking': '1' if blocking else '0'
        }

        response = requests.get(
            f'{self._ENDPOINT}/api/v1/vc/query',
            params=params,
            headers=self._headers
        )

        if response.status_code != 200:
            raise Exception(f"查询结果失败: {response.text}")

        return response.json()

    def generate_caption(self,
                         audio_url: Optional[str] = None,
                         audio_data: Optional[bytes] = None,
                         max_retry: int = 10,
                         retry_interval: int = 2) -> dict[str, any]:
        """
        生成字幕的完整流程
        
        Args:
            audio_url: 音频文件URL
            audio_data: 音频文件二进制数据
            max_retry: 最大重试次数
            retry_interval: 重试间隔(秒)
            
        Returns:
            Dict包含字幕结果
        """
        # 提交任务
        submit_result = self._submit_task(audio_url=audio_url, audio_data=audio_data)
        logger.info(f"提交识别任务:{submit_result}")
        task_id = submit_result['id']

        # 轮询查询结果
        retry_count = 0
        while retry_count < max_retry:
            result = self._query_result(task_id, blocking=False)

            if result['code'] == 0:  # 成功
                if not result.get('utterances'):
                    logger.info(f'生成字幕失败: utterances 返回为空{result.get("message")}')
                    return {}
                return result.get('utterances')

            elif result['code'] == 2000:  # 处理中
                logger.info(f'生成字幕处理中:{result} 等待次数{retry_count}')
                time.sleep(retry_interval)
                retry_count += 1
                continue
            else:  # 其他错误
                raise Exception(f"生成字幕失败: {result}")

        raise Exception("查询超时")


voice_ata = VoiceAta(appid='app_id', access_token='access_token')
