{"version": 1, "disable_existing_loggers": false, "filters": {"traceFilter": {"()": "core.logger_filters.TraceFilter"}, "pathnameFilter": {"()": "core.logger_filters.PathnameFilter"}}, "formatters": {"default": {"format": "%(asctime)s %(levelname)s [%(trace_id)s] %(location)s:%(lineno)d %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "INFO", "formatter": "default", "filters": ["traceFilter", "pathnameFilter"], "stream": "ext://sys.stdout"}, "file": {"class": "logging.handlers.TimedRotatingFileHandler", "level": "INFO", "formatter": "default", "filters": ["traceFilter", "pathnameFilter"], "filename": "__LOG_PATH_PLACEHOLDER__", "when": "midnight", "backupCount": 5, "encoding": "utf-8"}}, "loggers": {"elasticsearch": {"level": "WARNING", "propagate": false}, "werkzeug": {"level": "WARNING", "propagate": false}}, "root": {"level": "INFO", "handlers": ["console", "file"]}, "celery": {"level": "INFO", "handlers": ["console", "file"]}}