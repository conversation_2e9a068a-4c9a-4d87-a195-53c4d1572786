import logging
from fractions import Fraction

from celery import current_app

from repositories.material_es_repository import material_es_repository
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from repositories.video_file_repository import video_file_repository
from tasks.material_handle_tasks.base import BaseTask, TaskEnum
from video_handler.compress_handler import CompressHandler

logger = logging.getLogger(__name__)


class CompressTask(BaseTask):

    @classmethod
    def _get_code(cls) -> int:
        return TaskEnum.COMPRESS

    @classmethod
    def _get_name(cls) -> str:
        return '压缩视频'

    @classmethod
    def _is_file_once(cls) -> bool:
        return True

    @classmethod
    def _run(cls, video_file: VideoFileMetadata, material: Material):
        with CompressHandler(video_file_metadata=video_file) as compress_handler:
            # 压缩
            compress_handler.handle()

            # 上传压缩好的文件
            file_key, source_file_bak_key, low_quality_key = compress_handler.upload_compressed_file()

            # 获取压缩好的文件信息
            video_info = compress_handler.get_compress_video_info()

            # 更新视频信息
            video_file_repository.update_by_id(
                video_file.id,
                duration=int(float(Fraction(video_info['time_base']) * video_info['duration_ts']) * 1000),
                width=video_info['width'],
                height=video_info['height'],
                low_quality_key=low_quality_key
            )

            # 更新es
            material_es_repository.build_by_id(material.id)

        return True


@current_app.task(name='compress', max_retries=3, default_retry_delay=3, bind=True)
def compress(self, video_file_id: int, material_id: int):
    try:
        CompressTask.run(video_file_id, material_id)
    except Exception as e:
        logger.error(f'压缩视频失败: {e}')
        raise self.retry(exc=e)
