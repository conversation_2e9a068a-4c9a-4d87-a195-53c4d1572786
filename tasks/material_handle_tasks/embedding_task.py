import os

from celery import current_app

from repositories.material_es_repository import material_es_repository
from repositories.material_repository import material_repository
from repositories.material_vector_repository import material_vector_repository
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from repositories.vector.model.material_vector import FragmentVector
from repositories.video_file_repository import video_file_repository
from tasks.material_handle_tasks.base import BaseTask, TaskEnum
from video_handler.embedding_handler import EmbeddingHandler


class EmbeddingTask(BaseTask):

    SKIP_COMPRESSION = False

    @classmethod
    def _get_code(cls) -> int:
        return TaskEnum.EMBEDDING

    @classmethod
    def _get_name(cls) -> str:
        return '截帧嵌入'

    @classmethod
    def _run(cls, video_file: VideoFileMetadata, material: Material) -> bool:
        with Embedding<PERSON>and<PERSON>(video_file, material) as embed_handler:
            # 通过文件id重建向量，若当前文件已有向量则不需要再嵌入
            rebuild = material_vector_repository.rebuild_by_file_id(video_file.id)
            if rebuild:
                return True

            image_vector_map = embed_handler.handle(cls.SKIP_COMPRESSION)

            # 保存向量
            fragment_vector_list = []
            for image, embed in image_vector_map.items():
                clip_start = int(os.path.basename(image).split('_')[0])
                clip_duration = clip_start + 1
                fragment_vector_list.append(FragmentVector(
                    clip_start=clip_start,
                    clip_duration=clip_duration,
                    frame_vector=embed,
                    text_vector=[0.0] * 1024,
                    tags=['tags']
                ))

            material_vector_repository.add_file_vector(video_file, material, fragment_vector_list)

            video_file_repository.update_by_id(video_file.id, state=1)
            material_es_repository.build_by_file_id(video_file.id)

        return True


@current_app.task(name='embedding')
def embedding(video_file_id: int, material_id: int):
    EmbeddingTask.run(video_file_id, material_id)
