import logging

from celery import current_app

from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from repositories.video_file_insight_repository import video_file_insight_repository
from tasks.material_handle_tasks.base import BaseTask, TaskEnum

logger = logging.getLogger(__name__)


class LLMInsightTask(BaseTask):
    """
    llm理解视频
    """

    @classmethod
    def _get_code(cls) -> int:
        return TaskEnum.LLM_INSIGHT

    @classmethod
    def _get_name(cls) -> str:
        return 'llm视频理解'

    @classmethod
    def _run(cls, video_file: VideoFileMetadata, material: Material) -> bool:
        exist = video_file_insight_repository.get_file_insight(video_file.id)
        if exist:
            return True

        # 提交嵌入任务
        from tasks.material_handle_tasks.embedding_task import embedding
        embedding.delay(video_file.id, material.id)

        return True


@current_app.task(name='LLMUnderstand')
def llm_insight(video_file_id: int, material_id: int):
    LLMInsightTask.run(video_file_id, material_id)
