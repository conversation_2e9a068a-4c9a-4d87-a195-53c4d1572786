from celery import current_app

from core.ext import oss_ext
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from repositories.video_file_ata_repository import video_file_ata_repository
from sdk.face_detection import face_detection
from tasks.material_handle_tasks.base import BaseTask, TaskEnum
from video_handler.voice_insight_handler import VoiceInsightHandler


class VoiceInsightTask(BaseTask):

    @classmethod
    def _get_name(cls) -> str:
        return '音频理解'

    @classmethod
    def _get_code(cls) -> int:
        return TaskEnum.VOICE_INSIGHT

    @classmethod
    def _is_file_once(cls) -> bool:
        return True

    @classmethod
    def _run(cls, video_file: VideoFileMetadata, material: Material) -> bool:
        with VoiceInsightHandler(video_file) as voice_insight_handler:
            # 语音识别
            utterances = voice_insight_handler.handle()
            if utterances is None:
                return True

            median_time_list = []
            for utterance in utterances:
                utterance['median_time'] = str(
                    int((utterance['end_time'] - utterance['start_time']) / 2) + utterance['start_time'])
                median_time_list.append(utterance['median_time'])
            median_time_list_str = ','.join(median_time_list)

            face_map = face_detection.detection(oss_ext.replace_domain(video_file.key), median_time_list_str)

            for utterance in utterances:
                utterance['face'] = face_map.get(str(utterance.get('median_time')))

            # 保存结果
            video_file_ata_repository.save(video_file.id, utterances)

        return True


@current_app.task(name='voiceInsight',queue='voice-insight')
def voice_insight(video_file_id: int, material_id: int):
    VoiceInsightTask.run(video_file_id, material_id)