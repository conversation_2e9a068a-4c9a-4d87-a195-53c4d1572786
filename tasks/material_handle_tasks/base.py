import logging
import traceback

from core.constant import LOCK_PREFIX
from core.ext import redlock_ext
from repositories.material_repository import material_repository
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from repositories.mysql.model.video_file_task import TASK_STATE_SUCCESS, VideoFileTask
from repositories.video_file_repository import video_file_repository
from repositories.video_file_task_repository import video_file_task_repository

logger = logging.getLogger(__name__)


class TaskEnum:
    COMPRESS = 1
    LLM_INSIGHT = 2
    EMBEDDING = 3
    VOICE_INSIGHT = 4
    SHAKE_DETECTION = 5


class BaseTask:

    @classmethod
    def run(cls, file_id, material_id):
        logger.info(f'视频任务「{cls._get_name()}」开始处理 file_id:{file_id},material_id{material_id}')
        if not file_id:
            return
        lock_key = f'{LOCK_PREFIX}:video-task:{cls._get_code()}:{file_id}'

        # 10分钟不释放
        lock = redlock_ext.get_lock(lock_key)

        if not lock:
            logger.info(f'拿锁失败')
            return

        task = cls._init_task(file_id, material_id)

        # 判断任务状态
        if task.state == TASK_STATE_SUCCESS:
            logger.info(f'视频任务「{cls._get_name()}」已成功处理 task:{task.to_json()}')
            lock.release()
            return

        try:
            video_file = video_file_repository.get_by_id(file_id)
            material = material_repository.get_by_id(material_id)

            result = cls._run(video_file, material)

            logger.info(f'视频任务「{cls._get_name()}」处理结果 result:{result} ')

            if result:
                video_file_task_repository.success(task.id)
        except Exception as e:
            logger.error(f'视频任务「{cls._get_name()}」处理异常 {traceback.format_exc()}')
            video_file_task_repository.error(task.id, str(e)[:250])
            raise e
        finally:
            lock.release()

    @classmethod
    def _init_task(cls, file_id, material_id) -> VideoFileTask:
        # 一次性任务
        if cls._is_file_once():
            material_id = 0

        task = video_file_task_repository.get_task(file_id, material_id, cls._get_code())

        logger.info(f'''获取任务「{cls._get_name()}」task:{task.to_json() if task is not None else 'null'}''')
        if task is None:
            # 初始化视频任务
            logger.info(f'初始化任务「{cls._get_name()}」file_id:{file_id},material_id:{material_id}')
            task = video_file_task_repository.init(file_id, material_id, cls._get_code())

        return task

    @classmethod
    def _get_log_flag(cls, file_id, material_id):
        return f'「file_id:{file_id},material_id:{material_id}」'

    @classmethod
    def _run(cls, video_file: VideoFileMetadata, material: Material) -> bool:
        pass

    @classmethod
    def _get_code(cls) -> int:
        pass

    @classmethod
    def _get_name(cls) -> str:
        pass

    @classmethod
    def _is_file_once(cls) -> bool:
        return False
