from celery import current_app

from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from tasks.material_handle_tasks.base import BaseTask, TaskEnum
from video_handler.shake_detection_handler import ShakeDetectionHandler


class ShakeDetection(BaseTask):

    @classmethod
    def _get_name(cls) -> str:
        return '抖动检测'

    @classmethod
    def _get_code(cls) -> int:
        return TaskEnum.SHAKE_DETECTION

    @classmethod
    def _is_file_once(cls) -> bool:
        return True

    @classmethod
    def _run(cls, video_file: VideoFileMetadata, material: Material) -> bool:
        with ShakeDetectionHandler(video_file) as shake_detection_handler:
            shake_detection_handler.handle()

            return True


@current_app.task(name='shakeDetection')
def shake_detection(video_file_id: int, material_id: int):
    ShakeDetection.run(video_file_id, material_id)
