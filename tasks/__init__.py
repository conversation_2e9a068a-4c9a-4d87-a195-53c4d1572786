from celery import signals
from flask import Flask

from core.logging_config import setup_logging
from tasks.material_handle_tasks.compress_task import compress
from tasks.material_handle_tasks.llm_insight_task import llm_insight
from tasks.material_handle_tasks.embedding_task import embedding
from tasks.material_handle_tasks.shake_detection_task import shake_detection
from tasks.material_handle_tasks.voice_insight_task import voice_insight


@signals.after_setup_task_logger.connect
@signals.after_setup_logger.connect
def setting_logging(**kwargs):
    setup_logging('celery')


def register_task(app: Flask):
    app.extensions['celery'].register_task(compress)
    app.extensions['celery'].register_task(llm_insight)
    app.extensions['celery'].register_task(embedding)
    app.extensions['celery'].register_task(voice_insight)
    app.extensions['celery'].register_task(shake_detection)
