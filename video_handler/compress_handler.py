import logging
import os.path
from typing import Optional

from core.exception.ServiceException import ServiceException
from core.ext import oss_ext
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from utils import video_util, ffmpeg_util, video_path_util
from video_handler.handler import Handler

logger = logging.getLogger(__name__)


class CompressHandler(Handler):

    def __init__(self, video_url: Optional[str] = None, video_file_metadata: Optional[VideoFileMetadata] = None):
        super().__init__(video_file_metadata)
        self._video_url = video_url
        self._video_file_metadata = video_file_metadata

        if self._video_url:
            self._download()
        elif self._video_file_metadata:
            self._video_url = video_file_metadata.key
            self._download()
        else:
            raise ServiceException('参数错误 video_file, video_url, video_file_metadata 不能同时为空')

        self._compressed_file = os.path.join(self._temp_dir, 'compressed.mp4')

        self._low_quality_compressed_file = os.path.join(self._temp_dir, 'low_quality_compressed.mp4')

    def handle(self):

        logger.info(f"开始压缩视频（普通压缩）:{self._video_file}")
        self.default_compress()

        logger.info(f"开始压缩视频（低质量）:{self._video_file}")
        self.low_quality_compress()

    def default_compress(self):
        """
        默认压缩
        :return:
        """
        if ffmpeg_util.default_compress(self._video_file, self._compressed_file):
            return self._compressed_file
        return None

    def low_quality_compress(self):
        """
        低质量视频压缩
        :return:
        """
        if ffmpeg_util.low_quality_compress(self._video_file, self._low_quality_compressed_file):
            return self._compressed_file
        return None

    def get_compress_video_info(self):
        """
        获取压缩后的视频信息
        :return:
        """
        return video_util.get_video_info(self._compressed_file)

    def upload_compressed_file(self):
        if self._video_url is None:
            raise ServiceException('video_url 不能为空')

        file_key, source_file_bak_key, low_quality_key = video_path_util.parse_file_key(self._video_url)

        # 备份原始的文件
        logger.info(f'备份原始文件 {file_key} -> {source_file_bak_key}')
        oss_ext.client.copy_object(
            source_bucket_name=oss_ext.client.bucket_name,
            source_key=file_key,
            target_key=source_file_bak_key
        )

        # 替换成已压缩的文件
        logger.info(f'上传压缩文件 {file_key}')
        oss_ext.client.put_object_from_file(file_key, self._compressed_file)

        # 上传低质量的版本
        logger.info(f'上传低质量文件 {low_quality_key}')
        oss_ext.client.put_object_from_file(low_quality_key, self._low_quality_compressed_file)
        return file_key, source_file_bak_key, low_quality_key

    def _download(self):
        self._video_file = oss_ext.download(self._video_url, dirname=self._temp_dir)
