import logging
import os.path
from typing import Optional

from core.ext import oss_ext, volcengine_ext
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from utils import ffmpeg_util
from video_handler.handler import Handler

logger = logging.getLogger(__name__)


class VoiceInsightHandler(Handler):

    def __init__(self, video_file_metadata: Optional[VideoFileMetadata]):
        super().__init__(video_file_metadata)
        self._voice_file = os.path.join(self._temp_dir, 'voice.mp3')

    def handle(self):
        self._download_video_file()

        logger.info(f'开始抽取音频: {self._video_file} -> {self._voice_file}')

        if not ffmpeg_util.to_voice(self._video_file, self._voice_file):
            logger.info('ffmpeg 音频抽取失败')
            return None

        with open(self._voice_file, 'rb') as voice:
            utterances = volcengine_ext.voice_ata.generate_caption(audio_data=voice)

        if not utterances:
            logger.info('音频转文字失败 utterances 为空')
            return None

        return utterances
