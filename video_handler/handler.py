import logging
import os
import shutil
import tempfile
from typing import Self, Optional

from core.ext import oss_ext
from repositories.mysql.model.video_file_metadata import VideoFileMetadata

logger = logging.getLogger(__name__)


class Handler:

    def __init__(self,video_file_metadata: Optional[VideoFileMetadata]):
        self._video_file_metadata = video_file_metadata
        self._video_file = None
        self._temp_dir = tempfile.mkdtemp()
        logger.info(f"初始化临时目录:{self._temp_dir}")

    def __enter__(self) -> Self:
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if os.path.exists(self._temp_dir):
            shutil.rmtree(self._temp_dir)

    def _download_video_file(self):
        self._video_file = oss_ext.download(self._video_file_metadata.key, dirname=self._temp_dir)
