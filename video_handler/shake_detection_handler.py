import logging
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Optional

import cv2
import numpy as np

from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from repositories.video_file_insight_repository import video_file_insight_repository
from video_handler.handler import Handler

logger = logging.getLogger(__name__)

class ShakeDetectionHandler(Handler):

    def __init__(self, video_file_metadata: Optional[VideoFileMetadata]):
        super().__init__(video_file_metadata)

    def handle(self):
        self._download_video_file()

        logger.info(f'开始检测视频抖动: {self._video_file}')
        frame_container = FrameContainer(self._video_file)

        logger.info(f'开始计算位移: {self._video_file}')
        displacement = Displacement.get_displacement(frame_container)

        logger.info('开始计算抖动: {self._video_file}')
        result = ShakeDetection.calculate_shake_second(displacement)

        shake_seconds = '-1'
        frame_container.release()

        if len(result) > 0:
            shake_seconds = ",".join([str(second) for second in sorted(result)])
        logger.info(f'抖动检测结果: {shake_seconds}')

        video_file_insight_repository.save_shake_detection(self._video_file_metadata.id, shake_seconds)



class FrameContainer:

    def __init__(self, video_path):
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        self.pre_frame = None
        self.cur_frame = None
        self.frame_index = 0

        ret, frame = self.cap.read()
        if ret:
            self.cur_frame = frame
            self.frame_index += 1

    def read_frame(self):
        for _ in range(int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))):
            ret, frame = self.cap.read()
            if ret:
                self.frame_index += 1
                self.pre_frame = self.cur_frame
                self.cur_frame = frame
                yield self.pre_frame, self.cur_frame, self.frame_index
            else:
                return

    def release(self):
        self.cap.release()

class Displacement:
    """
    帧与帧之间的路径检测
    """

    @classmethod
    def get_displacement(cls, frame_container: FrameContainer):
        pool = ThreadPoolExecutor(max_workers=4)
        tasks = []
        block_displacement = {}

        for pre_frame, cur_frame, frame_index in frame_container.read_frame():
            pre_frame = cv2.cvtColor(pre_frame, cv2.COLOR_BGR2GRAY)
            cur_frame = cv2.cvtColor(cur_frame, cv2.COLOR_BGR2GRAY)
            tasks.append(pool.submit(cls._get_broder_displacement, pre_frame, cur_frame, frame_index))

        for task in tasks:
            res, frame_s = task.result()
            block_displacement[frame_s] = res
        pool.shutdown(wait=True)

        return np.array(
            [block_displacement[frame_s] for frame_s in sorted(block_displacement.keys())]
        )

    @classmethod
    def _get_broder_displacement(cls, gray_pre, gray_cur, frame):
        prev_kps = DENSE().detect(gray_pre)

        prev_kps = np.array([kp.pt for kp in prev_kps], dtype='float32').reshape(-1, 1, 2)

        flow, status, error = cv2.calcOpticalFlowPyrLK(gray_pre, gray_cur, prev_kps, None)

        good_flow = flow[status == 1]
        good_kps = prev_kps[status == 1]

        displacement = good_flow - good_kps

        y = np.sort(displacement[:, 0])
        x = np.sort(displacement[:, 1])
        mean_x = np.mean(x[int(len(x) * 0.15):int(len(x) * 0.85)])
        mean_y = np.mean(y[int(len(y) * 0.15):int(len(y) * 0.85)])
        gray_pre = None
        gray_cur = None

        return [mean_y, mean_x], frame

    @classmethod
    def find_dense_region(cls, data):
        rows, cols = data.shape
        y_step = rows // 10
        x_step = cols // 10

        max_region = None
        max_hit = 0
        # 构建累加和矩阵
        for y in range(0, rows, y_step):
            for x in range(0, cols, x_step):
                region = data[y:y + y_step, x:x + x_step]
                hit_count = len(np.where(region.flatten() == 1)[0])
                if hit_count > max_hit:
                    max_hit = hit_count
                    max_region = (x, y)

        mask = np.zeros((1920, 1080), dtype=int)
        for y in range(max_region[1], max_region[1] + y_step):
            for x in range(max_region[0], max_region[0] + x_step):
                mask[y, x] = 1

        return mask, [max_region, (max_region[0] + x_step, max_region[1] + y_step)]


class DENSE:
    def __init__(self, step=30, radius=.5):
        self.step = step
        self.radius = radius

    def detect(self, img):
        kps = []

        for x in range(0, img.shape[1], self.step):
            for y in range(0, 480, self.step):
                kps.append(cv2.KeyPoint(x, y, .0))

        for x in range(0, img.shape[1], self.step):
            for y in range(1440, 1920, self.step):
                kps.append(cv2.KeyPoint(x, y, .0))
        return kps

    def setInt(self, var, val):
        if var == "initXyStep":
            self.step = val

class ShakeDetection:

    @classmethod
    def calculate_shake_second(cls, block_displacement, std_threshold=0.63, std_interval_threshold=9,
                               continuous_shake_threshold=2):
        # 计算抖动
        result = []
        x_shake = cls._shake_calculate(block_displacement[:, 0], std_threshold, std_interval_threshold,
                                       continuous_shake_threshold)
        y_shake = cls._shake_calculate(block_displacement[:, 1], std_threshold, std_interval_threshold,
                                       continuous_shake_threshold)

        [result.append(x) for x in x_shake]
        [result.append(y) for y in y_shake]

        return sorted(set(result))

    @classmethod
    def _shake_calculate(cls, displacement, std_threshold, diff_interval_threshold, continuous_shake_threshold):
        diff = np.abs(displacement[1:] - displacement[:-1])

        bool_block_displacement = displacement >= 0

        wave_index = bool_block_displacement[:-1] == bool_block_displacement[1:]

        shake_index_list = np.where(wave_index == False)[0]

        filter_shake_index = []

        shake_index_set = set(shake_index_list)

        for shake_index in shake_index_list:
            interval_start = shake_index - diff_interval_threshold
            interval_end = shake_index + diff_interval_threshold
            if interval_start < 0:
                interval_start = 0
                interval_end = interval_start + diff_interval_threshold * 2
            if interval_end > displacement.shape[0]:
                interval_end = displacement.shape[0]
                interval_start = interval_end - diff_interval_threshold * 2

            shake_interval = diff[interval_start:interval_end]

            diff_std = np.std(shake_interval)

            if diff_std > std_threshold and len(
                    set(range(shake_index - 5, shake_index + 5)) & shake_index_set) >= continuous_shake_threshold:
                filter_shake_index.append(shake_index)
        filter_shake_index = np.array(filter_shake_index)

        return set((filter_shake_index / 30).astype(int))
