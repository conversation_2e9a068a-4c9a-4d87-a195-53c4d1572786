import logging
import os.path
from typing import Optional
from fractions import Fraction

from core.ext import oss_ext
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from sdk.clip_ai_yk import clip_ai_yk
from utils import ffmpeg_util, video_path_util, video_util
from video_handler.handler import Handler

logger = logging.getLogger(__name__)


class EmbeddingHandler(Handler):

    def __init__(self, video_file_metadata: Optional[VideoFileMetadata] = None, material: Optional[Material] = None):
        super().__init__(video_file_metadata)
        self._material = material

        name, suffix = video_path_util.get_video_name_suffix(self._video_file_metadata.key)

        self._video_compress_file = os.path.join(self._temp_dir, f'{name}_quick_compressed.{suffix}')

    def handle(self, skip_compression = False):
        self._download_video_file()
        if skip_compression:
            self._video_compress_file = self._video_file
            logger.info("跳过视频压缩")
        else:
            if not ffmpeg_util.quick_compress(self._video_file, self._video_compress_file):
                logger.info("视频文件快速压缩失败")

        # 与压缩任务并行，重新获取一次视频信息
        video_info = video_util.get_video_info(self._video_compress_file)
        self._video_file_metadata.duration = int(float(Fraction(video_info['time_base']) * video_info['duration_ts']) * 1000)
        self._video_file_metadata.width = video_info['width']
        self._video_file_metadata.height = video_info['height']

        # 视频截帧
        frame_dir = video_util.clip_frame_optimized_2(self._video_compress_file,max_workers=8)
        frame_list = [os.path.join(frame_dir, frame) for frame in os.listdir(frame_dir)]

        # 图像嵌入
        return clip_ai_yk.embed_images(frame_list)

