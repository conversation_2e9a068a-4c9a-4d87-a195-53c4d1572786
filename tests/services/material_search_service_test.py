from flask import Flask



def test_llm_search(app: Flask):
    with app.app_context():
        from services.material_search_service import material_search_service, MaterialLLmSearchEngine
        result = material_search_service.search(
            MaterialLLmSearchEngine,
            keyword='您这边平时会用什么屁股',
            materialIds=['463382793205188180']
        )
        print(result['list'])
        result = material_search_service.search(
            MaterialLLmSearchEngine,
            keyword='你这边平时会',
            materialIds=['463382793205188180']
        )
        print(result['list'])
def test_match_clip_time(app:Flask):
    with app.app_context():
        from repositories.video_file_ata_repository import video_file_ata_repository
        from services.material_search_service import MaterialLLmSearchEngine
        ata = video_file_ata_repository.get_by_file_id(426961230022122237)
        MaterialLLmSearchEngine.match_clip_time('会有什么',ata)
