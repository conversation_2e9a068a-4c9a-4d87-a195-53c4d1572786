from concurrent.futures import Thread<PERSON>oolExecutor

from flask import <PERSON>lask


def test_incr_usage_amount(app: Flask):
    with app.app_context():
        from services.material_usage_amount_service import material_usage_amount_service
        material_usage_amount_service.incr([
            {'materialId': 2, 'start': 0, 'end': 6},
            {'materialId': 2, 'start': 3, 'end': 5},
            {'materialId': 2, 'start': 1, 'end': 9},
            {'materialId': 2, 'start': 8, 'end': 10},
            {'materialId': 2, 'start': 2, 'end': 5},
        ])
