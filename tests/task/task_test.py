from flask import Flask


def test_shake_detection(app: Flask):
    with app.app_context():
        from tasks.material_handle_tasks.shake_detection_task import shake_detection
        ids = [256728058050908406, 256728058050973942, 256728058051039478, 256728062345875702, 256728062345941238, 256728062346006774, 256728062346072310, 256728062346137846, 256728062346268918, 256728062346334454, 256728062346399990, 256728062346465526, 256728062346531062, 256728062346596598, 256728062346662134, 256728062346727670, 256728062346793206, 256728062346858742, 256728062346924278, 256728062346989814, 256728066640842998, 256728066640908534, 256728066640974070, 256728066641039606, 256728066641105142, 256728066641629430, 256728066641694966, 256728066641760502, 256728066641826038, 256728066641891574, 256728066641957110, 256728070935810294, 256728070935875830, 256728070935941366, 256728070936006902, 256728070936072438, 256728070936137974, 256728070936203510, 256728075230843126, 256728075230908662, 256728075230974198, 256728075231039734, 256728075231105270, 256728075231170806, 256728075231236342, 256728075231301878, 256728075231367414, 256728075231432950, 256728075231498486, 256728075231564022, 256728075231695094, 256728075231760630, 256728075231891702, 256728079526596854, 256728079526662390, 256728079526727926, 256728079526793462, 256728079526858998, 256728088115679478, 256728088115745014, 256728088115810550, 256728088115876086, 256728088115941622, 256728088116007158, 256728088116072694, 256728088116138230, 256728088116203766, 256728088116269302, 256728088116334838, 256728088116400374, 256728088116465910, 256728088116531446, 256728088116596982, 256728088116662518, 256728088116728054, 256728088116793590, 256728088116859126, 256728088116924662, 256728092410646774, 256728092410712310, 256728092410777846, 256728092410843382, 256728092410908918, 256728092410974454, 256728092411039990, 256728092411105526, 256728092411171062, 256728092411236598, 256728092411302134, 256728092411367670, 256728092411433206, 256728092411498742, 256728092411564278, 256728092411629814, 256728092411695350, 256728092411760886, 256728092411826422, 256728092411891958, 256728096705614070, 256728096705679606, 256728096705745142, 256728096705810678, 256728096705876214, 256728096705941750, 256728096706007286, 256728096706072822, 256728096706138358, 256728096706203894, 256728096706269430, 256728096706334966, 256728096706400502, 256728096706466038, 256728096706531574, 256728096706597110, 256728096706662646, 256728096706728182, 256728096706793718, 256728096706859254, 256728096706924790, 256728101000581366, 256728101000646902, 256728101000712438, 256728101000777974, 256728101000843510, 256728101000909046, 256728101000974582, 256728101001040118, 256728101001105654, 256728101001171190, 256728101001236726, 256728101001302262, 256728101001367798, 256728101001433334, 256728101001498870, 256728101001564406, 256728101001629942, 256728101001695478, 256728101001761014, 256728101001826550, 256728101001892086, 256728105295548662, 256728105295614198, 256728105295679734, 256728105295745270, 256728105295810806, 256728105295876342, 256728105295941878, 256728105296007414, 256728105296072950, 256728105296138486, 256728105296204022, 256728105296269558, 256728105296335094, 256728105296400630, 256728105296466166, 256728105296531702, 256728105296597238, 256728105296662774, 256728109590515958, 256728109590581494, 256728109590647030, 256728109590712566, 256728109590778102, 256728109590843638, 256728109590909174, 256728109590974710, 256728109591040246, 256728109591105782, 256728109591171318, 256728109591236854, 256728109591302390, 256728109591367926, 256728109591433462, 256728109591498998, 256728109591630070, 256728109591695606, 256728109591761142, 256728113885483254, 256728113885548790, 256728113885614326, 256728113885679862, 256728113885745398, 256728113885810934, 256728113885876470, 256728113885942006, 256728113886007542, 256728113886073078, 256728113886138614, 256728113886204150, 256728113886269686, 256728113886335222, 256728113886400758, 256728113886466294, 256728113886531830, 256728113886597366, 256728113886662902, 256728113886728438, 256728122475483382, 256728122475548918, 256728122475614454, 256728122475679990, 256728122475745526, 256728122475811062, 256728122475876598, 256728122475942134, 256728122476007670, 256728122476073206, 256728122476138742, 256728122476204278, 256728122476269814, 256728122476335350, 256728122476400886, 256728122476466422, 256728122476531958, 256728122476597494, 256728122476663030, 256728126770385142, 256728126770450678, 256728126770516214, 256728126770581750, 256728126770647286, 256728126770712822, 256728126771106038, 256728131065549046, 256728131065614582, 256728131065680118, 256728131065745654, 256728131065811190, 256728131065876726, 256728131065942262, 256728131066007798, 256728131066073334, 256728131066138870, 256728131066204406, 256728131066269942, 256728131066335478, 256728131066401014, 256728131066532086, 256728131066597622, 256728135360319734, 256728135360385270, 256728135360516342, 256728135360581878, 256728135360647414, 256728135360712950, 256728135360778486, 256728135360844022, 256728135360909558, 256728135360975094, 256728135361040630, 256728135361106166, 256728135361433846, 256728135361499382, 256728139655352566, 256728139655418102, 256728139655483638, 256728139655549174, 256728139655614710, 256728139655680246, 256728139655942390, 256728139656007926, 256728139656073462, 256728139656138998, 256728143950844150, 256728143950909686, 256728143950975222, 256728143951040758, 256728148245287158, 256728148245352694, 256728148245418230, 256728148245549302, 256728148245614838, 256728148245680374, 256728148245745910, 256728148245811446, 256728148245942518, 256728148246008054, 256728148246073590, 256728148246139126, 256728148246204662, 256728148246270198, 256728152540188918, 256728152540844278, 256728152540909814, 256728152540975350, 256728152541040886, 256728152541106422, 256728152541171958, 256728152541237494, 256728152541303030, 256728152541368566, 256728152541434102, 256728156835156214, 256728156835221750, 256728156835287286, 256728156835352822, 256728156835418358, 256728156835483894, 256728156835549430, 256728156835614966, 256728156835680502, 256728156835746038, 256728156835811574, 256728161130320118, 256728161130385654, 256728161130451190, 256728161130516726, 256728161130582262, 256728161130647798, 256728161130713334, 256728161130778870, 256728161130844406, 256728161130909942, 256728161130975478, 256728161131041014, 256728161131106550, 256728161131172086, 256728161131237622, 256728161131303158, 256728165425090806, 256728165425156342, 256728165425221878, 256728165425287414, 256728165425352950, 256728165425418486, 256728165425484022, 256728165425549558, 256728165425615094, 256728165425680630, 256728165425746166, 256728165425811702, 256728165425877238, 256728165425942774, 256728165426008310, 256728165426073846, 256728165426139382, 256728165426204918, 256728165426270454, 256728165426335990, 256728169720058102, 256728169720123638, 256728169720189174, 256728169720254710, 256728169720320246, 256728169720385782, 256728169720451318, 256728169720516854, 256728169720582390, 256728169720647926, 256728169720844534, 256728169720910070, 256728169720975606, 256728169721041142, 256728169721106678, 256728169721172214, 256728174015025398, 256728174015090934, 256728174015156470, 256728174015222006, 256728174015287542, 256728174015353078, 256732593536372982, 256732593536438518, 256732597831340278, 256732597831405814, 256732597831471350, 256732597831536886, 256732597831602422, 256732597831667958, 256732597831733494, 256732597831799030, 256732597831864566, 256732597831930102, 256732597831995638, 256732597832061174, 256732597832126710, 256732597832192246, 256732602126307574, 256732602126373110, 256732602126438646, 256732602126504182, 256732602126569718, 256732602126635254, 256732602126700790, 256732602126766326, 256732602126831862, 256732602126897398, 256732602126962934, 256732606421274870, 256732606421340406, 256732606421405942, 256732606421471478, 256732606421668086, 256732606421733622, 256732606421799158, 256732606421864694, 256732606421930230, 256732606421995766, 256732606422061302, 256732606422126838, 256732606422192374, 256732606422257910, 256732606422323446, 256732606422388982, 256732610716373238, 256732610716438774, 256732610716504310, 256732610716569846, 256732610716635382, 256732610716700918, 256732615011471606, 256732615011537142, 256732615011602678, 256732615011668214, 256732615011733750, 256732615011799286, 256732615011864822, 256732615011930358, 256732615011995894, 256732615012061430, 256732615012126966, 256732615012192502, 256732615012258038, 256732615012323574, 256732615012389110, 256732615012454646, 256732619306176758, 256732619306242294, 256732619306307830, 256732619306373366, 256732619306438902, 256732619306504438, 256732619306569974, 256732619306635510, 256732619306701046, 256732619306766582, 256732619306832118, 256732619306897654, 256732619306963190, 256732619307028726, 256732619307094262, 256732619307159798, 256732619307225334, 256732623601144054, 256732623601209590, 256732623601275126, 256732623601340662, 256732623601406198, 256732623601471734, 256732623601537270, 256732623601602806, 256732623601668342, 256732623601733878, 256732623601799414, 256732623601864950, 256732623602061558, 256732623602127094, 256732623602192630, 256732623602258166, 256732623602323702, 256732627896111350, 256732627896176886, 256732627896242422, 256732627896307958, 256732627896373494, 256732627896439030, 256732627896504566, 256732627896570102, 256732627896635638, 256732627896701174, 256732627896766710, 256732627896832246, 256732627896897782, 256732627896963318, 256732627897028854, 256732627897094390, 256732627897159926, 256732627897225462, 256732627897290998, 256732627897356534, 256732632191078646, 256732632191144182, 256732632191209718, 256732632191275254, 256732632191340790, 256732632191406326, 256732632191471862, 256732632191537398, 256732632191602934, 256732632191668470, 256732632191734006, 256732632191799542, 256732632191865078, 256732632191930614, 256732632191996150, 256732632192061686, 256732632192127222, 256732632192192758, 256732632192258294, 256732636486045942]

        for video_file_id in ids:
            shake_detection.delay(video_file_id, 0)

'''
wget https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/video/257793433278681143_1_2.mp4.mp4 && wget https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/video/257796349561409592_1_2.mp4.mp4 && wget https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/video/257799141290151992_1_2.mp4.mp4 && wget https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/video/257800816327397432_1_1.mp4.mp4 && wget https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/video/257800816327397432_1_2.mp4.mp4 && wget https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/video/257802615918694456_1_1.mp4.mp4 && wget https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/video/257802615918694456_1_2.mp4.mp4 && wget https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/video/257813821488369721_1_2.mp4.mp4 
'''