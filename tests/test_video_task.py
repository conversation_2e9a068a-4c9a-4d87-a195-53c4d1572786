import traceback

from flask import <PERSON>lask


def test_add_material(app: Flask):
    with app.app_context():
        from tasks import compress, voice_insight, llm_insight
        try:
            # 压缩
            compress.delay(1, 1)

            # 大模型理解
            llm_insight.delay(1, 1)

            # 语音识别
            voice_insight.delay(1, 1)
        except Exception as e:
            print(traceback.print_exc())
