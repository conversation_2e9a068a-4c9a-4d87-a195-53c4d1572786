from flask import <PERSON>lask


def test_shake_detection(app: Flask):
    with app.app_context():
        from video_handler.shake_detection_handler import ShakeDetectionHandler
        from repositories.video_file_repository import video_file_repository
        video_file_metadata = video_file_repository.get_by_id(256728062346137846)

        with ShakeDetectionHand<PERSON>(video_file_metadata) as shake_detection_handler:
            shake_detection_handler.handle()
