from flask import Flask

from repositories.material_repository import material_repository


def test_shake_detection(app: Flask):
    with app.app_context():
        from video_handler.embedding_handler import Embedding<PERSON>andler
        from repositories.video_file_repository import video_file_repository
        video_file = video_file_repository.get_by_id(435091796381866110)
        material = material_repository.get_by_id(475534084614063213)

        with Embedding<PERSON>andler(video_file, material, EmbeddingHandler.EMBEDDING_MODEL_JINA) as embed_handler:
            result = embed_handler.handle()
            print(result)
