from flask import Flask


def test_dify_client(app: Flask):
    with app.app_context():
        from sdk.dify.subtitle_match_app import subtitle_match_app

        response = subtitle_match_app.run({
            'subtitle': '[{\"id\": 468156984361753928, \"subtitle\": \"你们公司确实不错，那八十到一百平的，现在简约风要多少钱啊？你用手机？哎，别说什么注册下载，我就是嫌麻烦才来你们店里面算的。您误会了，现在点击视频下方链接，不用下载，不用注册，个人信息输入房屋面积和所在地，就能获取你家的装修报价了。我们的报价都是由本地的装修数据而生成的，保证是真实可靠，设计施工、主材辅材、家具家电、窗帘、灯具等一站式全包。而且欧派家的装修材料还可以享受工厂直采价，拒绝中间商赚差价，项目清晰透明，无恶意增项，预约成功还安排专业团队一对一服务，从开工到竣工全程跟进。如果你半年内准备要装修，赶紧点击视频下方链接免费咨询装修报价吧！\"}]',
            'match': '从开工到竣工'
        }, 'TEST_1')
        print(response)
