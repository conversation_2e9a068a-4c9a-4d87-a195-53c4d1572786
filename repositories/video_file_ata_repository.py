import json
from datetime import datetime

from sqlalchemy import select, update

from core.decorators import transactional
from core.ext import db
from repositories.mysql.model.video_file_ata import VideoFileAta


class VideoFileAtaRepository:

    @staticmethod
    def get_by_file_id(file_id: int) -> VideoFileAta:
        query = select(VideoFileAta).where(
            VideoFileAta.file_id == file_id,
            VideoFileAta.deleted == 0
        )

        return db.session.execute(query).scalar_one_or_none()

    @staticmethod
    def get_by_file_ids(file_ids: list[int]) -> dict[int, VideoFileAta]:
        query = select(VideoFileAta).where(
            VideoFileAta.file_id.in_(file_ids),
            VideoFileAta.deleted == 0
        )
        return {video_file_ata.file_id: video_file_ata for video_file_ata in db.session.execute(query).scalars().all()}

    @staticmethod
    @transactional
    def save(file_id: int, utterances: dict) -> None:
        query = select(VideoFileAta).where(
            VideoFileAta.file_id == file_id
        )
        utterances_str = json.dumps(utterances, ensure_ascii=False)

        exist = db.session.execute(query).scalar()
        if not exist:
            video_file_ata = VideoFileAta(file_id=file_id, utterances=utterances_str, create_time=datetime.now())
            db.session.add(video_file_ata)
        else:
            update_sql = update(VideoFileAta).where(
                VideoFileAta.file_id == file_id
            ).values(utterances=utterances_str, deleted=0)

            db.session.execute(update_sql)

        db.session.commit()


video_file_ata_repository = VideoFileAtaRepository()
