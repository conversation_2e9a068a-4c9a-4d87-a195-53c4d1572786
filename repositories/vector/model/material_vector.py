import logging
from typing import Optional

import arrow
from pydantic import BaseModel
from pymilvus import FieldSchema, DataType

from core.func import is_vertical
from repositories.material_folder_tree import material_folder_tree
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata


class BaseVectorModel(BaseModel):

    @staticmethod
    def get_mapping():
        return {
            'properties': {
            }
        }

    @staticmethod
    def get_name():
        return 'default'

    def to_json(self):
        return self.model_dump_json()


class FragmentVector(BaseModel):
    clip_start: int
    clip_duration: int
    frame_vector: list[float]
    text_vector: list[float]
    tags: list[str]


class MaterialVector(BaseVectorModel):
    id: Optional[int] = None

    user_id: int

    organize_id: int

    file_id: int

    material_id: int

    frame_vector: Optional[list[float]] = None

    text_vector: Optional[list[float]] = None

    folder_ids: list[int]

    vertical: bool

    clip_start: int

    clip_duration: int

    duration: int

    tags: list[str]

    create_time: int

    @classmethod
    def parse(cls, video_file: VideoFileMetadata, material: Material, fragment: FragmentVector):
        return MaterialVector(
            file_id=video_file.id,
            user_id=material.user_id,
            organize_id=material.organize_id,
            material_id=material.id,
            frame_vector=fragment.frame_vector,
            text_vector=fragment.text_vector,
            folder_ids=material_folder_tree.get_tree(material.user_id, material.organize_id).get_folder_path_ids(
                material.folder_id,
            ),
            vertical=is_vertical(video_file.width, video_file.height, video_file.rotate),
            clip_start=fragment.clip_start,
            clip_duration=fragment.clip_duration,
            duration=video_file.duration,
            tags=fragment.tags,
            create_time=arrow.get(material.create_time).int_timestamp * 1000
        )

    def setting_material_info(self, material: Material):
        self.folder_ids = material_folder_tree.get_tree(material.user_id, material.organize_id).get_folder_path_ids(
            material.folder_id,
        )
        self.material_id = material.id

    def setting_file_info(self, video_file: VideoFileMetadata):
        self.vertical = is_vertical(video_file.width, video_file.height, video_file.rotate)
