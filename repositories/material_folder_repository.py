import logging

from datetime import datetime
from typing import Any

from sqlalchemy import select, func, update

from core.decorators import transactional
from repositories.material_folder_tree import material_folder_tree
from repositories.mysql.model.material_folder import MaterialFolder

from core.ext import db, snowflake_ext
from repositories.mysql.model.material_folder_link import MaterialFolderLink

logger = logging.getLogger(__name__)


class MaterialFolderRepository:

    @staticmethod
    @transactional
    def get_folder_or_create(
            path: str,
            user_id: int,
            organize_id: int,
            pid: int = 0,
            is_client: bool = False,
            sort: int = 100
    ) -> MaterialFolder | None | Any:
        """
        获取或创建素材目录，如果path是多级目录，会创建所有层级的目录
        :param path: 完整路径，如 /aaa/bbb/ccc
        :param user_id: 用户ID
        :param organize_id: 企业ID
        :param pid: 父级ID，默认为0
        :param is_client: 客户端
        :param sort: 排序
        :return: 最底层的MaterialFolder对象
        """
        if not path:
            return None

        # 分割路径，去掉空字符串
        path_parts = [p for p in path.split('/') if p]
        current_path = ''
        current_pid = pid
        result_folder = None
        folders_to_create = []

        # 先判断是否存在完整路径
        existing_folder = db.session.query(MaterialFolder).filter(
            MaterialFolder.path == path,
            MaterialFolder.user_id == user_id,
            MaterialFolder.organize_id == organize_id,
            MaterialFolder.deleted == 0
        ).with_for_update().first()
        if existing_folder:
            # 如果存在，直接返回
            return existing_folder

        for part in path_parts:
            current_path = f"{current_path}/{part}" if current_path else f"/{part}"

            # 使用 SELECT FOR UPDATE NOWAIT 避免死锁
            folder = db.session.query(MaterialFolder).filter(
                MaterialFolder.path == current_path,
                MaterialFolder.user_id == user_id,
                MaterialFolder.organize_id == organize_id,
                MaterialFolder.deleted == 0
            ).with_for_update().first()

            if not folder:
                # 如果不存在，准备创建新文件夹
                folder = MaterialFolder(
                    id=snowflake_ext.next_id(),
                    pid=current_pid,
                    user_id=user_id,
                    organize_id=organize_id,
                    name=part,
                    path=current_path,
                    is_client=is_client,
                    sort=sort if sort else 0,
                    deleted=0,
                    create_time=datetime.now()
                )
                folders_to_create.append(folder)
                db.session.add(folder)

            current_pid = folder.id
            result_folder = folder

        material_folder_tree.remove_tree(user_id, organize_id)
        return result_folder

    @classmethod
    def get_by_ids(cls, ids: list[int]):
        query = select(MaterialFolder).filter(MaterialFolder.id.in_(ids), MaterialFolder.deleted == 0)

        return db.session.execute(query).scalars().all()

    @classmethod
    def get_by_id(cls, id: int) -> MaterialFolder:
        query = select(MaterialFolder).filter(MaterialFolder.id == id, MaterialFolder.deleted == 0)

        return db.session.execute(query).scalars().first()

    @classmethod
    def check_children_name_unique(cls, pid, name):
        query = select(func.count()).select_from(MaterialFolder).filter(
            MaterialFolder.pid == pid,
            MaterialFolder.name == name,
            MaterialFolder.deleted == 0
        )

        return db.session.execute(query).scalars().first() > 0

    @classmethod
    def update_folder(cls, id, update_fields):
        update_ = update(MaterialFolder).where(MaterialFolder.id == id).values(**update_fields)

        return db.session.execute(update_).rowcount > 0

    @classmethod
    def link_organize(cls, sourceId: int, targetId: int, cid: int):
        link = MaterialFolderLink(
            source_id=sourceId,
            target_id=targetId,
            user_id=cid,
            create_time=datetime.now()
        )

        db.session.add(link)

        return True

    @classmethod
    def get_folder_link(cls, sourceId: int) -> MaterialFolder:
        query = select(MaterialFolderLink).where(
            MaterialFolderLink.source_id == sourceId,
            MaterialFolderLink.deleted == 0
        )
        linked = db.session.execute(query).scalars().first()

        target_folder_id = linked.target_id if linked else None

        if target_folder_id:
            return cls.get_by_id(target_folder_id)
        else:
            return None


    @classmethod
    def link_organize_exist(cls, sourceId: int, targetId: int):
        exist_query = select(func.count()).select_from(MaterialFolderLink).where(
            MaterialFolderLink.source_id == sourceId,
            MaterialFolderLink.target_id == targetId,
            MaterialFolderLink.deleted == 0
        )

        return db.session.execute(exist_query).scalar() > 0

    @classmethod
    def get_linked_ids(cls, cid: int):
        query = select(MaterialFolderLink).where(
            MaterialFolderLink.user_id == cid,
            MaterialFolderLink.deleted == 0
        )

        return {item.source_id: item.target_id for item in db.session.execute(query).scalars().all()}


material_folder_repository = MaterialFolderRepository()
