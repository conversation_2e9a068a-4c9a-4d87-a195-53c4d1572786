import json
import logging
import time
from concurrent.futures.thread import ThreadPoolExecutor
from typing import Optional

import arrow
from flask import current_app
from sqlalchemy import select

from core.ext import db
from repositories.es.base_es_repository import BaseEsRepository
from repositories.es.model.MaterialDoc import MaterialDoc
from repositories.mysql.model.material import Material
from repositories.mysql.model.material_folder import MaterialFolder
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from utils import es_util

logger = logging.getLogger(__name__)


class MaterialEsRepository(BaseEsRepository):
    _doc_model = MaterialDoc
    _thread_pool: ThreadPoolExecutor = ThreadPoolExecutor(max_workers=8)

    @classmethod
    def get_by_folder_id(cls, folder_id):
        query = {"query": {"bool": {"must": [{'term': {'folder_ids': folder_id}}]}}}

        return es_util.get_hits_source_list(material_es_repository.base_search(body=query))

    @classmethod
    def get_entity_by_ids(cls, ids):
        material_query = select(Material).where(Material.id.in_(ids), Material.deleted == 0)

        return db.session.execute(material_query).scalars().all()

    @classmethod
    def search_(
            cls, cid, oid,
            ids: Optional[list[int]] = None,
            folder_ids: Optional[list[int]] = None,
            vertical: Optional[int] = None,
            create_time_start: Optional[str] = None,
            create_time_end: Optional[str] = None,
            pageNo: int = 1,
            pageSize: int = 20
    ):
        query = cls._to_es_query(ids, folder_ids, vertical, create_time_start, create_time_end, cid, oid)

        query['from'] = (pageNo - 1) * pageSize
        query['size'] = pageSize
        query['sort'] = [{'create_time': {'order': "desc"}},{'id': {'order': "desc"}}]

        logger.info(f'es 搜索条件：{json.dumps(query)}')

        return material_es_repository.base_search(body=query)

    @classmethod
    def get_by_ids(cls, ids) -> list[MaterialDoc]:
        query = {"query": {"bool": {"must": [{'terms': {'id': ids}}]}}, "size": len(ids)}

        doc_list = es_util.get_hits_source_list(material_es_repository.base_search(body=query))

        material_doc_list = []

        for material_doc in doc_list:
            material_doc = MaterialDoc(**material_doc)
            low_quality_url = material_doc.low_quality_url
            if low_quality_url and len(low_quality_url) > 0:
                material_doc.url = low_quality_url

            material_doc_list.append(material_doc)

        return material_doc_list

    @classmethod
    def _to_es_query(
            cls,
            ids: Optional[list[int]] = None,
            folderIds: Optional[list[int]] = None,
            vertical: Optional[int] = None,
            createTimeStart: Optional[str] = None,
            createTimeEnd: Optional[str] = None,
            cid: int = None,
            oid: int = None
    ):
        if ids is not None and isinstance(ids, list) and len(ids) > 0:
            return {'query': {'bool': {'must': [{'terms': {'id': ids}}]}}}
        # 用户上传的素材
        user_material = {'bool': {'must': [{'term': {'organize_id': oid}}, {'term': {'user_id': cid}}]}}

        # 企业的素材
        enterprise_material = {'bool': {'must': [{'term': {'organize_id': oid}}, {'term': {'user_id': 0}}]}}

        should = []
        # 个人素材
        if -1 in folderIds:
            folderIds.remove(-1)
            should.append(user_material)
        # 企业素材
        if -2 in folderIds:
            folderIds.remove(-2)
            should.append(enterprise_material)

        if len(should) == 0:
            should = [user_material, enterprise_material]

        # 基本范围
        must = [{'bool': {'should': should}}]

        if folderIds and len(folderIds) > 0:
            must.append({'terms': {'folder_ids': folderIds}})

        # 横竖屏
        if vertical is not None:
            if vertical in [1,2]:
                must.append({'term': {'vertical': vertical==1}})

        # 时间筛选
        create_time_range = {}
        if createTimeStart is not None:
            # 转换成00:00:00的时间戳
            create_time_start_timestamp = arrow.get(createTimeStart).floor('day').int_timestamp * 1000
            create_time_range['gt'] = create_time_start_timestamp
        if createTimeEnd is not None:
            # 转换成23:59:59的时间戳
            create_time_end_timestamp = arrow.get(createTimeEnd).ceil('day').int_timestamp * 1000 - 1
            create_time_range['lt'] = create_time_end_timestamp
        if len(create_time_range):
            must.append({'range': {'create_time': create_time_range}})

        return {
            "query": {
                "bool": {
                    "must": must
                }
            }
        }

    @classmethod
    def _next_docs_page(cls, last_id: int):
        material_query = select(Material).where(Material.id > last_id).order_by(Material.id.asc()).limit(1000)

        material_list = db.session.execute(material_query).scalars().all()

        return cls._to_docs(material_list=material_list)

    @classmethod
    def _to_docs(cls, material_ids: Optional[list] = None, material_list: Optional[list] = None):
        start_time = time.time()
        if material_ids:
            material_list = cls.get_entity_by_ids(material_ids)

        if not material_list or len(material_list) == 0:
            return []

        # 获取文件信息
        video_file_ids = set([material.file_id for material in material_list])
        video_file_query = select(VideoFileMetadata).where(VideoFileMetadata.id.in_(video_file_ids))
        video_file_list = db.session.execute(video_file_query).scalars().all()
        video_file_map = {video_file.id: video_file for video_file in video_file_list}
        fetch_time = time.time() - start_time
        logger.info(f'获取文件耗时: {fetch_time:.2f}秒')

        # 获取目录信息
        folder_ids = set([material.folder_id for material in material_list])
        folder_query = select(MaterialFolder.id, MaterialFolder.name).select_from(MaterialFolder).where(
            MaterialFolder.id.in_(folder_ids),
            MaterialFolder.deleted == 0)
        folder_list = db.session.execute(folder_query).all()
        folder_map = {folder.id: folder.name for folder in folder_list}
        fetch_time = time.time() - start_time
        logger.info(f'获取目录耗时: {fetch_time:.2f}秒')

        app = current_app._get_current_object()

        def parse(*args):
            with app.app_context():
                return MaterialDoc.parse(*args)

        task_list = [
            cls._thread_pool.submit(parse, material, video_file_map[material.file_id], folder_map.get(material.folder_id))
            for material in material_list if video_file_map.get(material.file_id)
        ]

        docs = [task.result() for task in task_list]
        fetch_time = time.time() - start_time
        logger.info(f'解析素材耗时: {fetch_time:.2f}秒')

        return docs


material_es_repository = MaterialEsRepository()
