from sqlalchemy import DateTime, String, TIMESTAMP, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, TINYINT
from sqlalchemy.orm import Mapped, mapped_column
import datetime

from repositories.mysql.model.base_model import BaseModel


class Material(BaseModel):
    __tablename__ = 'material'
    __table_args__ = {'comment': '素材表'}

    id: Mapped[int] = mapped_column(BIGINT(20), primary_key=True)
    user_id: Mapped[int] = mapped_column(BIGINT(20), comment='用户id')
    organize_id: Mapped[int] = mapped_column(BIGINT(20), comment='企业id')
    source_id: Mapped[int] = mapped_column(BIGINT(20), comment='来源的素材id self.id (同步，复制等)')
    file_id: Mapped[int] = mapped_column(BIGINT(20), comment='文件id video_file_metadata.id')
    folder_id: Mapped[int] = mapped_column(BIGINT(20), comment='文件所在目录id material_folder.id')
    local_path: Mapped[str] = mapped_column(String(512), server_default=text("''"), comment='文件本地目录path')
    file_name: Mapped[str] = mapped_column(String(128), server_default=text("''"), comment='文件名')
    type: Mapped[int] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='素材类型 1:空镜 2:口播')
    deleted: Mapped[int] = mapped_column(TINYINT(4), server_default=text("'0'"), comment='是否删除')
    create_time: Mapped[datetime.datetime] = mapped_column(DateTime, comment='创建时间')
    update_time: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text(
        'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间')
