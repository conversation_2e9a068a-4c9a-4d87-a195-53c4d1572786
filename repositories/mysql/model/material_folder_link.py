from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, DateTime, Integer, TIMESTAMP, text
from sqlalchemy.dialects.mysql import TINYINT
from sqlalchemy.orm import Mapped, mapped_column
from repositories.mysql.model.base_model import BaseModel
import datetime


class MaterialFolderLink(BaseModel):
    __tablename__ = 'material_folder_link'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    source_id: Mapped[int] = mapped_column(BigInteger, comment='源目录id')
    target_id: Mapped[int] = mapped_column(BigInteger, comment='目标目录id')
    user_id: Mapped[int] = mapped_column(BigInteger, comment='用户id')
    deleted: Mapped[int] = mapped_column(TINYINT, server_default=text("'0'"), comment='是否删除')
    create_time: Mapped[datetime.datetime] = mapped_column(DateTime, comment='创建时间')
    update_time: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text(
        'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间')
