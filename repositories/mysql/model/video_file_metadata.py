from sqlalchemy import DateTime, String, TIMESTAMP, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, TINYINT
from sqlalchemy.orm import Mapped, mapped_column
import datetime

from repositories.mysql.model.base_model import BaseModel


class VideoFileMetadata(BaseModel):
    __tablename__ = 'video_file_metadata'
    __table_args__ = {'comment': '文件底层表'}

    id: Mapped[int] = mapped_column(BIGINT(20), primary_key=True)
    key: Mapped[str] = mapped_column(String(255), comment='视频文件oss key')
    low_quality_key: Mapped[str] = mapped_column(String(255), server_default=text("''"), comment='低质量oss key')
    width: Mapped[int] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='视频宽')
    height: Mapped[int] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='视频高')
    duration: Mapped[int] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='视频时长')
    rotate: Mapped[int] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='视频旋转参数')
    state: Mapped[int] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='视频状态 0:初始化 1:可用')
    deleted: Mapped[int] = mapped_column(TINYINT(4), server_default=text("'0'"), comment='是否删除')
    create_time: Mapped[datetime.datetime] = mapped_column(DateTime, comment='创建时间')
    update_time: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text(
        'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间')
