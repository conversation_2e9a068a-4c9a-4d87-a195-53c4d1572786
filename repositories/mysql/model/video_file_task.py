from sqlalchemy import DateTime, String, TIMESTAMP, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, TINYINT
from sqlalchemy.orm import Mapped, mapped_column
import datetime

from repositories.mysql.model.base_model import BaseModel


class VideoFileTask(BaseModel):
    __tablename__ = 'video_file_task'
    __table_args__ = {'comment': '处理任务队列'}

    id: Mapped[int] = mapped_column(INTEGER(11), primary_key=True)
    file_id: Mapped[int] = mapped_column(BIGINT(20), comment='文件id')
    material_id: Mapped[int] = mapped_column(BIGINT(20), comment='素材id')
    task: Mapped[int] = mapped_column(INTEGER(11), comment='任务编号 1:压缩 2:xxx 待定')
    state: Mapped[int] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='0:未执行 -1:执行失败')
    error_msg: Mapped[str] = mapped_column(String(255), server_default=text("''"), comment='错误信息')
    retry: Mapped[int] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='重试次数')
    deleted: Mapped[int] = mapped_column(TINYINT(4), server_default=text("'0'"), comment='是否删除')
    create_time: Mapped[datetime.datetime] = mapped_column(DateTime, comment='创建时间')
    update_time: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text(
        'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间')


TASK_STATE_FAIL = -1

TASK_STATE_SUCCESS = 1

TASK_STATE_INIT = 0
