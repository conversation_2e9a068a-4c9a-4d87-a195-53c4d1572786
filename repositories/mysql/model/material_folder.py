from enum import Enum

from sqlalchemy import DateTime, String, TIMESTAMP, text, INT, INTEGER
from sqlalchemy.dialects.mysql import BIGINT, TINYINT
from sqlalchemy.orm import Mapped, mapped_column
import datetime
from repositories.mysql.model.base_model import BaseModel


class MaterialFolder(BaseModel):
    __tablename__ = 'material_folder'
    __table_args__ = {'comment': '素材目录'}

    id: Mapped[int] = mapped_column(BIGINT(20), primary_key=True)
    pid: Mapped[int] = mapped_column(BIGINT(20), server_default=text("'0'"), comment='父级id')
    user_id: Mapped[int] = mapped_column(BIGINT(20), comment='用户id')
    organize_id: Mapped[int] = mapped_column(BIGINT(20), comment='企业id')
    name: Mapped[str] = mapped_column(String(128), comment='目录名')
    path: Mapped[str] = mapped_column(String(512), comment='完整path')
    is_client: Mapped[int] = mapped_column(TINYINT(4), server_default=text("'0'"), comment='是否客户端')
    sort: Mapped[int] = mapped_column(INTEGER(), server_default=text("'0'"), comment='排序')
    deleted: Mapped[int] = mapped_column(TINYINT(4), server_default=text("'0'"), comment='是否删除')
    create_time: Mapped[datetime.datetime] = mapped_column(DateTime, comment='创建时间')
    update_time: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text(
        'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间')


class EntityType(Enum):
    # 个人
    PERSONAL = 1

    # 企业
    ENTERPRISE = 2

class SpecialFolderId(Enum):

    # 个人根目录id
    PERSONAL = -1

    # 企业根目录id
    ENTERPRISE = -2