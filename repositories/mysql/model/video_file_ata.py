from typing import Optional

from sqlalchemy import DateTime, TIMESTAMP, text
from sqlalchemy.dialects.mysql import BIGINT, LONGTEXT, TINYINT
from sqlalchemy.orm import Mapped, mapped_column
import datetime

from repositories.mysql.model.base_model import BaseModel


class VideoFileAta(BaseModel):
    __tablename__ = 'video_file_ata'
    __table_args__ = {'comment': '语音解析信息'}

    file_id: Mapped[int] = mapped_column(BIGINT(20), primary_key=True, comment='文件id')
    deleted: Mapped[int] = mapped_column(TINYINT(4), server_default=text("'0'"), comment='是否删除')
    create_time: Mapped[datetime.datetime] = mapped_column(DateTime, comment='创建时间')
    update_time: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text(
        'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间')
    utterances: Mapped[Optional[str]] = mapped_column(LONGTEXT, comment='视频语音信息')
