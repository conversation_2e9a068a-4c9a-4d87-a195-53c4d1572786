from typing import Optional

from sqlalchemy import DateTime, String, TIMESTAMP, Text, text
from sqlalchemy.dialects.mysql import BIGINT, TINYINT
from sqlalchemy.orm import Mapped, mapped_column
import datetime

from repositories.mysql.model.base_model import BaseModel


class VideoFileInsight(BaseModel):
    __tablename__ = 'video_file_insight'
    __table_args__ = {'comment': 'llm解析信息'}

    file_id: Mapped[int] = mapped_column(BIGINT(20), primary_key=True, comment='文件id')
    tags: Mapped[str] = mapped_column(String(255), server_default=text("''"), comment='标签')
    segment: Mapped[str] = mapped_column(String(128), server_default=text("''"), comment='视频分段')
    desc: Mapped[str] = mapped_column(String(512), server_default=text("''"), comment='视频描述')
    keyword: Mapped[str] = mapped_column(String(512), server_default=text("''"), comment='视频关键文字描述')
    shake_seconds: Mapped[str] = mapped_column(String(1024), server_default=text("''"), comment='抖动秒数')
    deleted: Mapped[int] = mapped_column(TINYINT(4), server_default=text("'0'"), comment='是否删除')
    create_time: Mapped[datetime.datetime] = mapped_column(DateTime, comment='创建时间')
    update_time: Mapped[datetime.datetime] = mapped_column(TIMESTAMP, server_default=text(
        'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='更新时间')
    original_resp: Mapped[Optional[str]] = mapped_column(Text, comment='llm原始返回数据')
