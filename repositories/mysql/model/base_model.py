from flask import jsonify
from sqlalchemy import inspect

from core.ext import db


class BaseModel(db.Model):
    __abstract__ = True

    def to_dict(self, exclude=None):
        if exclude:
            return {c.key: getattr(self, c.key) for c in inspect(self).mapper.column_attrs if c.key not in exclude}
        return {c.key: getattr(self, c.key) for c in inspect(self).mapper.column_attrs}

    def to_json(self):
        return jsonify(self.to_dict()).get_json()
