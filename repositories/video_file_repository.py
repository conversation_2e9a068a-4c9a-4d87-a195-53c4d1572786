from sqlalchemy import update, select

from core.decorators import transactional
from core.ext import db
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from repositories.mysql.model.video_file_task import VideoFileTask


class VideoFileRepository:

    @classmethod
    def rotate_file(cls, rotate_file_list: list[dict]):
        file_list = cls.get_by_ids([video_file['id'] for video_file in rotate_file_list])
        file_map = {file.id: file for file in file_list}

        success_ids = []
        for rotate_file in rotate_file_list:
            current_video_file = file_map[rotate_file['id']]

            if current_video_file.width == 0 or current_video_file.height == 0:
                continue
            if rotate_file['rotate'] not in [0, 90, 180, 270]:
                continue
            if rotate_file['rotate'] == current_video_file.rotate:
                continue

            update_sql = update(VideoFileMetadata).where(
                VideoFileMetadata.id == rotate_file['id'],
                VideoFileMetadata.deleted == 0
            ).values(rotate=rotate_file['rotate'])

            db.session.execute(update_sql)
            success_ids.append(rotate_file['id'])

        db.session.commit()
        return success_ids

    @classmethod
    def get_by_ids(cls, ids):
        """
        根据id获取素材文件
        :param ids:
        :return:
        """
        query = select(VideoFileMetadata).filter(VideoFileMetadata.id.in_(ids), VideoFileMetadata.deleted == 0)

        return db.session.execute(query).scalars().all()

    @classmethod
    def filter_duration(cls, ids, duration):
        """
        过滤时长为0的素材
        :return:
        """
        query = select(VideoFileMetadata.id).select_from(VideoFileMetadata).filter(
            VideoFileMetadata.id.in_(ids),
            VideoFileMetadata.duration >= duration,
            VideoFileMetadata.deleted == 0
        )

        return db.session.execute(query).scalars()

    @classmethod
    def get_by_id(cls, id) -> VideoFileMetadata:
        """
        根据id获取素材文件
        :param id:
        :return:
        """
        query = select(VideoFileMetadata).filter(VideoFileMetadata.id == id, VideoFileMetadata.deleted == 0)

        return db.session.execute(query).scalar()

    @classmethod
    @transactional
    def update_by_id(self, id, **fields):
        """
        根据key获取素材文件
        :param key:
        :return:
        """
        update_ = update(VideoFileMetadata).where(VideoFileMetadata.id == id).values(**fields)

        return db.session.execute(update_).rowcount

    @classmethod
    @transactional
    def set_file_build_finish(cls, id):
        """
        设置文件构建完成
        :param id:
        :return:
        """
        file_finish_task_query = select(VideoFileTask.task).where(VideoFileTask.file_id == id, VideoFileTask.state == 1)

        file_task_ids = db.session.execute(file_finish_task_query).scalars().all()

        update_ = update(VideoFileMetadata).where(VideoFileMetadata.id == id).values(state=1)

        return db.session.execute(update_).rowcount


video_file_repository = VideoFileRepository()
