import arrow
from sqlalchemy import select, update

from core.decorators import transactional
from core.ext import db

from repositories.mysql.model.video_file_task import VideoFileTask, TASK_STATE_INIT, TASK_STATE_SUCCESS, \
    TASK_STATE_FAIL


class VideoFileTaskRepository:

    @classmethod
    @transactional
    def get_task(cls, file_id, material_id, code) -> VideoFileTask:
        query = select(VideoFileTask).where(
            VideoFileTask.file_id == file_id,
            VideoFileTask.material_id == material_id,
            VideoFileTask.deleted == 0,
            VideoFileTask.task == code
        )

        return db.session.execute(query).scalar()

    @classmethod
    @transactional
    def init(cls, file_id, material_id, task_code) -> VideoFileTask:
        video_file_task = VideoFileTask(
            file_id=file_id,
            material_id=material_id,
            task=task_code,
            state=TASK_STATE_INIT,
            retry=0,
            deleted=0,
            create_time=arrow.now().datetime,
        )
        db.session.add(video_file_task)
        db.session.flush()

        return video_file_task

    @classmethod
    @transactional
    def success(cls, task_id):
        query = update(VideoFileTask).where(
            VideoFileTask.id == task_id,
        ).values(state=TASK_STATE_SUCCESS)

        return db.session.execute(query).rowcount

    @classmethod
    @transactional
    def error(cls, task_id, error_msg: str):
        query = update(VideoFileTask).where(
            VideoFileTask.id == task_id,
        ).values(state=TASK_STATE_FAIL, error_msg=error_msg)

        return db.session.execute(query).rowcount


video_file_task_repository = VideoFileTaskRepository()
