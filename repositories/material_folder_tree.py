from anytree import Node, search, PreOrderIter
from anytree.exporter import DictExporter
from anytree.importer import Dict<PERSON>mporter
from sqlalchemy import select
import json

from core.constant import CACHE_PREFIX
from core.ext import db, redis_client
from repositories.mysql.model.material_folder import MaterialFolder, EntityType


class MaterialFolderTree:

    def __init__(self):
        self.dict_exporter = DictExporter()
        self.dict_importer = DictImporter()

    def _get_cache_key(self, user_id: int, organize_id: int) -> str:
        return f'{CACHE_PREFIX}:material_folder_tree:data:{user_id}:{organize_id}'

    def _get_tree_from_cache(self, user_id: int, organize_id: int, expected_version: str):
        cache_key = self._get_cache_key(user_id, organize_id)
        tree_data = redis_client.get(cache_key)
        if tree_data:
            try:
                tree_dict = json.loads(tree_data)
                root_node = self.dict_importer.import_(tree_dict)
                # 检查版本是否匹配
                if hasattr(root_node, 'version') and str(root_node.version) == str(expected_version):
                    return root_node
                # 版本不匹配，删除缓存
                redis_client.delete(cache_key)
            except Exception:
                # 如果反序列化失败，删除缓存
                redis_client.delete(cache_key)
        return None

    def _save_tree_to_cache(self, user_id: int, organize_id: int, root_node: Node):
        cache_key = self._get_cache_key(user_id, organize_id)
        tree_dict = self.dict_exporter.export(root_node)
        # 递归处理字典，确保所有值都可以被 JSON 序列化
        tree_dict = self._convert_bytes_to_str(tree_dict)
        # 设置缓存，使用较长的过期时间
        redis_client.set(cache_key, json.dumps(tree_dict), ex=86400)  # 缓存1天

    def _convert_bytes_to_str(self, data):
        """递归转换字典中的 bytes 类型为 str 类型"""
        if isinstance(data, bytes):
            return data.decode('utf-8')
        elif isinstance(data, dict):
            return {key: self._convert_bytes_to_str(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._convert_bytes_to_str(item) for item in data]
        return data

    def get_tree(self, user_id: int = 0, organize_id: int = 0) -> "FolderTreeHandler":
        version = self.get_version(user_id, organize_id)

        # 从 Redis 获取缓存的树
        root_node = self._get_tree_from_cache(user_id, organize_id, version)
        if root_node:
            return FolderTreeHandler(root_node)

        query = select(MaterialFolder).filter(
            MaterialFolder.deleted == 0,
            MaterialFolder.organize_id == organize_id,
            MaterialFolder.user_id == user_id
        )

        folder_list: list[MaterialFolder] = db.session.execute(query).scalars().all()

        folder_map = {}

        root_tree = Node(f'{user_id}:{organize_id}', id=0, version=version)

        for folder in folder_list:
            node_dict = {
                'id': folder.id,
                'name': folder.name,
                'pid': folder.pid,
                'sort': folder.sort,
                'type': EntityType.PERSONAL.value if user_id != 0 else EntityType.ENTERPRISE.value,
                'is_client_sync': folder.is_client == 1
            }
            folder_map[folder.id] = Node(**node_dict)
        for folder in folder_list:
            folder_map[folder.id].parent = root_tree if folder.pid == 0 else folder_map[folder.pid]

        # 保存到 Redis 缓存
        self._save_tree_to_cache(user_id, organize_id, root_tree)
        return FolderTreeHandler(root_tree)

    def remove_tree(self, user_id: int, organize_id: int):
        # 删除 Redis 缓存
        cache_key = self._get_cache_key(user_id, organize_id)
        redis_client.delete(cache_key)
        redis_client.incr(f'{CACHE_PREFIX}:material_folder_tree:{user_id}:{organize_id}')

    @classmethod
    def get_version(cls, user_id: int, organize_id: int):
        version = redis_client.get(f'{CACHE_PREFIX}:material_folder_tree:{user_id}:{organize_id}')
        if not version:
            return str(redis_client.incr(f'{CACHE_PREFIX}:material_folder_tree:{user_id}:{organize_id}'))
        # 确保返回字符串类型
        return version.decode('utf-8') if isinstance(version, bytes) else str(version)

    def get_dict(self, node):
        return self.dict_exporter.export(node)


class FolderTreeHandler:

    def __init__(self, root_node: Node):
        self.root_node = root_node

    def get_folder_path_ids(self, folder_id):
        res = search.find_by_attr(self.root_node, folder_id, name='id')
        if not res:
            return []

        return [node.id for node in res.path]

    def get_by_id(self, folder_id):
        return search.find_by_attr(self.root_node, folder_id, name='id')

    def get_path_by_id(self, folder_id):
        folder = self.get_by_id(folder_id)

        return '/'.join(node.name for node in folder.path if node.id)

    def get_by_ids(self, folder_ids):
        return search.findall(self.root_node, lambda node: node.id in folder_ids)

    def get_folder_path_ids_by_ids(self, folder_ids):
        folder_nodes = search.findall(self.root_node, lambda node: node.id in folder_ids)
        path_ids = set()
        for folder_node in folder_nodes:
            path_ids.update([node.id for node in folder_node.path])

        return list(path_ids)

    def get_node(self):
        return self.root_node

    def pre_order_iter(self):
        return PreOrderIter(self.get_node())


material_folder_tree = MaterialFolderTree()
