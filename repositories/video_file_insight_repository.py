import arrow
from sqlalchemy import select, update

from core.decorators import transactional
from core.ext import db
from repositories.mysql.model.video_file_insight import VideoFileInsight


class VideoFileInsightRepository:

    @classmethod
    def get_file_insight(cls, file_id) -> VideoFileInsight:
        query = select(VideoFileInsight).where(
            VideoFileInsight.file_id == file_id,
            VideoFileInsight.deleted == 0
        )

        return db.session.execute(query).scalar_one_or_none()

    @classmethod
    @transactional
    def save_or_update(cls, video_file_insight: VideoFileInsight):
        exist = cls.get_file_insight(video_file_insight.file_id)
        if exist:
            update_sql = update(VideoFileInsight).where(
                VideoFileInsight.file_id == video_file_insight.file_id
            ).values(**video_file_insight.to_dict(exclude={'file_id','create_time'}))

            db.session.execute(update_sql)
        else:
            db.session.add(video_file_insight)

    @classmethod
    @transactional
    def save_shake_detection(cls, file_id, shake_seconds):
        exist = cls.get_file_insight(file_id)
        if exist:
            update_sql = update(VideoFileInsight).where(
                VideoFileInsight.file_id == file_id
            ).values(shake_seconds=shake_seconds)

            db.session.execute(update_sql)
        else:
            video_file_insight = VideoFileInsight(
                file_id=file_id,
                shake_seconds=shake_seconds,
                create_time=arrow.now().datetime
            )
            db.session.add(video_file_insight)


video_file_insight_repository = VideoFileInsightRepository()
