import json
import logging
import time
from collections import defaultdict
from typing import Optional

import arrow

from core.ext import milvus_ext
from repositories.material_repository import material_repository
from repositories.mysql.model.material import Material
from repositories.mysql.model.material_folder import SpecialFolderId
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from repositories.vector.model.material_vector import FragmentVector, MaterialVector
from repositories.video_file_repository import video_file_repository

logger = logging.getLogger(__name__)


class MaterialVectorRepository:
    _COLLECTION_NAME = 'material_vector'

    @classmethod
    def add_file_vector(
            cls,
            video_file: VideoFileMetadata,
            material: Material,
            fragment_vector: list[FragmentVector]
    ):
        logger.info(f'向量入库 {video_file.to_dict()},{material.to_json()}')
        material_vector_list = [
            MaterialVector.parse(video_file, material, fragment).model_dump()
            for fragment in fragment_vector
        ]

        milvus_ext.client.upsert(cls._COLLECTION_NAME, material_vector_list)

    @classmethod
    def get_file_vector(cls, file_id: int) -> Optional[list[MaterialVector]]:
        """
        获取文件的向量
        :param file_id:
        :return:
        """
        min_material_id = material_repository.get_first_material_by_file_id(file_id)
        if not min_material_id:
            return None

        filter_str = f'file_id == {file_id} and material_id == {min_material_id}'

        results = milvus_ext.client.query(cls._COLLECTION_NAME, filter_str)

        return [MaterialVector(**result) for result in results]

    @classmethod
    def rebuild_by_file_id(cls, file_id: int):
        """
        通过文件id 重建向量中的素材
        """
        # 先获视频文件取所有的素材
        material_list = material_repository.get_by_file_id(file_id)
        material_ids = [material.id for material in material_list]

        # 尝试更新已有的向量
        updated, fail_material_ids = cls.update_materials_info(material_list)

        # 全部更新成功
        if updated:
            return True

        # 如果没有全部更新成功，代表视频文件有新的素材
        # 删除更新失败的素材id
        [material_ids.remove(fail_material_id) for fail_material_id in fail_material_ids]

        # 全部失败 表示当前视频文件从未嵌入过向量
        if len(material_ids) == 0:
            return False

        # 拿一个素材id用于复制一份向量
        source_material_id = material_ids[0]

        cls.copy_vector(source_material_id, [material for material in material_list if material.id not in material_ids])
        return True

    @classmethod
    def copy_vector(cls, source_id, target_material_list: list[Material]):
        source_material_vector = cls.get_by_material_ids([source_id])
        new_material_vector_list = []

        # 将id置空
        for material_vector in source_material_vector:
            material_vector.id = None

        for material in target_material_list:
            for material_vector in source_material_vector:
                logger.info(f'复制向量 {material_vector.id},{material_vector.material_id} -> {material.id}')
                new_material_vector = MaterialVector(**material_vector.model_dump())
                new_material_vector.setting_material_info(material)
                new_material_vector_list.append(new_material_vector.model_dump())

        milvus_ext.client.upsert(cls._COLLECTION_NAME, new_material_vector_list)

    @classmethod
    def update_material_info_by_ids(cls, material_ids):
        """
        根据material_ids 更新向量库中的素材信息 若全部成功返回 true,[] 否则返回 false,[没有成功的素材ids]
        """
        material_list = material_repository.get_material_by_ids(material_ids)

        return cls.update_materials_info(material_list)

    @classmethod
    def update_materials_info(cls, material_list: list[Material]):
        """
        根据material 更新向量库中的素材信息,视频文件信息 若全部成功返回 true,[] 否则返回 false,[没有成功的素材ids]
        """
        material_ids = [material.id for material in material_list]

        # 已有向量数据
        material_vector_list = cls.get_by_material_ids(material_ids)

        # 文件信息
        file_ids = [material.file_id for material in material_list]
        file_map = {file_metadata.id: file_metadata for file_metadata in video_file_repository.get_by_ids(file_ids)}

        # 素材信息
        material_map = {material.id: material for material in material_list}

        # 根据素材id分组
        material_vector_group: dict[int, list[MaterialVector]] = defaultdict(list)
        [material_vector_group[material_vector.material_id].append(material_vector)
         for material_vector in material_vector_list]

        # 更新向量中 素材，视频文件信息
        for material_id, material_vector_list in material_vector_group.items():
            for material_vector in material_vector_list:
                # 设置更新素材信息
                material_vector.setting_material_info(material_map[material_vector.material_id])
                # 设置更新文件信息
                material_vector.setting_file_info(file_map[material_vector.file_id])

            milvus_ext.client.upsert(
                cls._COLLECTION_NAME,
                [material_vector.model_dump() for material_vector in material_vector_list]
            )
            material_ids.remove(material_id)

        return len(material_ids) == 0, material_ids

    @classmethod
    def rebuild_by_material_ids(cls,material_ids):

        material_list = material_repository.get_material_by_ids(material_ids)
        logger.info(f'重建向量 {material_ids}, {len(material_list)}')

        cls.update_materials_info(material_list=material_list)

    @classmethod
    def get_by_material_ids(cls, material_ids: list[int]):
        filter_str = f'material_id in {json.dumps(material_ids)}'

        start = time.time()
        logger.info('开始获取向量信息')
        results = milvus_ext.client.query(cls._COLLECTION_NAME, filter_str)
        logger.info(f'结束获取向量信息: {time.time() - start}')

        return [MaterialVector(**result) for result in results]

    @classmethod
    def vector_query_file_id(
            cls,
            user_id: int,
            organize_id: int,
            vector_data,
            duration: int,
            excluded_ids: Optional[list[int]] = None,
            folder_ids: Optional[list[int]] = None,
            vertical: Optional[int] = None,
            create_time_start: Optional[str] = None,
            create_time_end: Optional[str] = None,
    ):
        # 组装搜索条件
        filter_str = cls._build_filter(user_id, organize_id, duration, excluded_ids, folder_ids, vertical,
                                       create_time_start,
                                       create_time_end)

        logger.info(f'向量搜索条件：{filter_str}')

        start = time.time()
        logger.info('开始获取向量搜索')
        result = milvus_ext.client.search(
            collection_name=cls._COLLECTION_NAME,
            data=vector_data,
            anns_field='frame_vector',
            filter=filter_str,
            limit=1000,
            output_fields=['file_id']
        )[0]
        logger.info(f'结束获取向量搜索: {time.time() - start}')

        # 通过file_id分组保留分数最高的那一条
        file_vector_map = defaultdict(list)
        [file_vector_map[hit['entity']['file_id']].append({
            'file_id': hit['entity']['file_id'],
            'score': hit['distance'],
        }) for hit in result]

        file_vector_list = [max(file_vector_group, key=lambda x: x['score']) for file_vector_group in file_vector_map.values()]

        return file_vector_list

    @classmethod
    def _build_filter(
            cls,
            user_id: int,
            organize_id: int,
            duration: int,
            excluded_ids: Optional[list[int]] = None,
            folder_ids: Optional[list[int]] = None,
            vertical: Optional[int] = None,
            create_time_start: Optional[str] = None,
            create_time_end: Optional[str] = None,
    ):

        person_scope = f'(user_id == {user_id} and organize_id == {organize_id})'
        enterprise_scope = f'(user_id == 0 and organize_id == {organize_id})'

        basic_filter = []

        if SpecialFolderId.PERSONAL.value not in folder_ids and SpecialFolderId.ENTERPRISE.value not in folder_ids:
            basic_filter.extend([person_scope, enterprise_scope])
        if SpecialFolderId.PERSONAL.value in folder_ids:
            basic_filter.append(person_scope)
            folder_ids.remove(SpecialFolderId.PERSONAL.value)
        if SpecialFolderId.ENTERPRISE.value in folder_ids:
            basic_filter.append(enterprise_scope)
            folder_ids.remove(SpecialFolderId.ENTERPRISE.value)

        filter_str = f'({' or '.join(basic_filter)}) and  duration >= {duration}'

        if excluded_ids:
            filter_str += f' and material_id not in {json.dumps(excluded_ids)}'

        if folder_ids:
            filter_str += f''' and ARRAY_CONTAINS_ANY(folder_ids,{json.dumps([int(folder_id) for folder_id in folder_ids])})'''

        if vertical:
            if vertical in [1, 2]:
                filter_str += f' and vertical == {vertical == 1}'

        if create_time_start:
            filter_str += f''' and create_time >= {arrow.get(create_time_start).floor('day').int_timestamp * 1000}'''

        if create_time_end:
            filter_str += f''' and create_time <= {arrow.get(create_time_end).ceil('day').int_timestamp * 1000}'''

        return filter_str

    def remove_vector(self, material_ids):
        return milvus_ext.client.delete(self._COLLECTION_NAME, filter=f'material_id in {json.dumps([int(id) for id in material_ids])}')


material_vector_repository = MaterialVectorRepository()
