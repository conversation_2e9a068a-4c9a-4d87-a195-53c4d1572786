import logging
from typing import Callable

import arrow

from sqlalchemy import select, update, func

from core.constant import STR_EMPTY
from core.decorators import transactional
from core.ext import db, snowflake_ext
from repositories.material_folder_repository import material_folder_repository
from repositories.mysql.model import material_folder
from repositories.mysql.model.material import Material
from repositories.mysql.model.material_folder import MaterialFolder
from repositories.mysql.model.video_file_metadata import VideoFileMetadata

logger = logging.getLogger(__name__)


class MaterialRepository:

    @classmethod
    @transactional
    def add_material(
        cls,
        folder: MaterialFolder,
        video_file: VideoFileMetadata,
        filename: str = None,
        callback: Callable[[any], None] = None,
    ) -> Material:
        """
        添加素材
        :param callback:
        :param folder:
        :param video_file:
        :return:
        """

        # 添加视频元数据
        video_file = cls._add_video_file_metadata(video_file)
        logger.info(f"视频文件元数据: {video_file.to_dict()}")

        # 判断素材是否存在
        exist_material = cls._is_material_exist(folder, video_file)

        if exist_material:
            logger.info(f"素材已存在: {video_file.key}更新目录，和文件名")
            update_data = {"local_path": folder.path}
            if filename:
                update_data["file_name"] = filename
            cls._update_material(id=exist_material.id, **update_data)
            material = exist_material
        else:
            material = Material(
                id=snowflake_ext.next_id(),
                folder_id=folder.id,
                file_id=video_file.id,
                local_path=folder.path,
                file_name=filename or STR_EMPTY,
                create_time=arrow.now().datetime,
                user_id=folder.user_id,
                organize_id=folder.organize_id,
                source_id=0,
            )
            copyed_material = cls.copy_material(material)

            db.session.add(material)

        # 最后同步到es
        if callback:
            result = callback(material.id)
            logger.info(f"{material.id} 同步到es: {result}")
            if exist_material and copyed_material:
                callback(copyed_material.id)
                logger.info(f"{copyed_material.id} 同步到es: {result}")

        return material

    @classmethod
    def copy_material(cls, source_material: Material):
        folder = material_folder_repository.get_folder_link(source_material.folder_id)
        if not folder:
            return
        logger.info(f"复制素材: {source_material.id}")

        material = Material(
            id=snowflake_ext.next_id(),
            folder_id=folder.id,
            file_id=source_material.file_id,
            local_path=folder.path,
            file_name=source_material.file_name,
            create_time=arrow.now().datetime,
            user_id=folder.user_id,
            organize_id=folder.organize_id,
            source_id=source_material.id,
        )
        db.session.add(material)

        return material

    def edit_material(self, id: int, fileName: str = None):
        return self._update_material(id, file_name=fileName)

    @classmethod
    def get_material_by_ids(cls, ids):
        """
        根据id获取素材
        :param ids:
        :return:
        """
        query = select(Material).filter(Material.id.in_(ids), Material.deleted == 0)

        return db.session.execute(query).scalars().all()

    @staticmethod
    def _is_material_file_exist(key: str) -> bool:
        """
        判断素材文件是否存在
        :param key:
        :return:
        """
        query = (
            select(VideoFileMetadata)
            .select_from(VideoFileMetadata)
            .filter(VideoFileMetadata.key == key, VideoFileMetadata.deleted == 0)
        )

        return db.session.execute(query).scalar()

    @classmethod
    def _is_material_exist(
        cls, folder: MaterialFolder, video_file: VideoFileMetadata
    ) -> Material | None:
        """
        判断素材是否存在
        :param folder:
        :param video_file:
        :return:
        """
        query = (
            select(Material)
            .select_from(Material)
            .filter(
                Material.folder_id == folder.id,
                Material.file_id == video_file.id,
                Material.deleted == 0,
            )
        )

        return db.session.execute(query).scalar()

    @classmethod
    def _add_video_file_metadata(cls, video_file: VideoFileMetadata):
        """
        添加视频文件元数据
        :param video_file:
        :return:
        """
        exist = cls._is_material_file_exist(video_file.key)

        if exist:
            return exist
        else:
            video_file.id = snowflake_ext.next_id()
            video_file.create_time = arrow.now().datetime

            # handlers = get_first_material_handler(current_app, video_file)

            # logger.info(f"获取到的素材处理器: {handlers}")
            #
            # for handler in handlers:
            #     handler.submit()

            db.session.add(video_file)

        return video_file

    @classmethod
    def _update_material(cls, id: int, **args):
        """
        更新素材
        :param id:
        :param args:
        :return:
        """
        query = update(Material).where(Material.id == id).values(**args)

        return db.session.execute(query).rowcount

    @classmethod
    def move_material(cls, material_ids, folder_id):
        """
        移动素材
        :param material_ids:
        :param folder_id:
        :return:
        """
        query = (
            update(Material)
            .where(Material.id.in_(material_ids))
            .values(folder_id=folder_id)
        )

        return db.session.execute(query).rowcount

    @classmethod
    def remove(cls, ids):
        """
        删除素材
        :param ids:
        :return:
        """
        query = update(Material).where(Material.id.in_(ids)).values(deleted=1)

        return db.session.execute(query).rowcount

    @classmethod
    def get_by_file_id(cls, file_id):
        """
        根据文件id获取素材
        :param file_ids:
        :return:
        """
        query = select(Material).where(
            Material.file_id == file_id, Material.deleted == 0
        )

        return db.session.execute(query).scalars().all()

    @classmethod
    def get_first_material_by_file_id(cls, file_id):
        """
        根据文件id获取素材
        :param file_ids:
        :return:
        """
        query = (
            select(func.min(Material.id))
            .where(Material.file_id == file_id, Material.deleted == 0)
            .group_by(Material.file_id)
        )

        return db.session.execute(query).scalar_one_or_none()

    @classmethod
    def get_by_id(cls, id):
        """
        根据id获取素材
        :param id:
        :return:
        """
        query = select(Material).where(Material.id == id, Material.deleted == 0)

        return db.session.execute(query).scalar_one_or_none()

    @classmethod
    def get_by_folder_id(cls, folder_id):
        """
        根据文件夹id获取素材
        :param folder_id:
        :return:
        """
        query = select(Material).where(
            Material.folder_id == folder_id, Material.deleted == 0
        )


material_repository = MaterialRepository()
