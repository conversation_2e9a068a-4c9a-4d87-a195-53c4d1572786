import json
import logging
import time
from typing import Optional

import arrow

from core.constant import ES_INDEX_PREFIX
from core.ext import elasticsearch_ext

from repositories.es.model.MaterialDoc import BaseDocModel
from repositories.material_repository import material_repository

logger = logging.getLogger(__name__)


class BaseEsRepository:
    _doc_model = BaseDocModel

    @classmethod
    def get_index_name(cls):
        return cls.__get_alis_name()

    @classmethod
    def creat_index(cls):
        alias_name = cls.__get_alis_name()

        index_name = f'{alias_name}-{arrow.now().format("YYYY_MM_DD_HH_mm_ss")}'

        body = {
            'mappings': cls._doc_model.get_mapping()
        }

        elasticsearch_ext.client.indices.create(index=index_name, body=body)
        return index_name

    @classmethod
    def build_by_id(cls, id):
        return cls.build_by_ids([id])

    @classmethod
    def build_by_ids(cls, ids: list):

        docs = cls._to_docs(ids)
        logger.info(f'同步文档:{json.dumps([doc.to_json() for doc in docs])}')

        if not docs:
            return

        cls.__index_docs(docs)

        return True

    @classmethod
    def build_by_file_id(cls, file_id):
        material_ids = [material.id for material in material_repository.get_by_file_id(file_id)]

        return cls.build_by_ids(material_ids)

    @classmethod
    def remove_docs(cls, ids):
        """
        删除文档
        :param ids:
        :return:
        """

        index_name = cls.__get_alis_name()
        query = {
            'query': {
                'bool': {'must': {
                    'terms': {
                        'id': ids
                    }
                }}
            }
        }

        return elasticsearch_ext.client.delete_by_query(
            index=index_name,
            body=query,
            wait_for_completion=True,
            refresh=True
        )

    @classmethod
    def base_search(cls, body):
        return elasticsearch_ext.client.search(index=cls.get_index_name(), body=body)

    @classmethod
    def rebuild(cls):
        """
        重建索引
        :return:
        """
        index_name = cls.creat_index()
        logger.info(f'创建索引:{index_name}')

        last_id = 0
        page = 0

        while True:
            start_time = time.time()
            docs = cls._next_docs_page(last_id)
            fetch_time = time.time() - start_time
            logger.info(f'获取文档耗时: {fetch_time:.2f}秒')
            
            if not docs:
                break

            start_time = time.time()
            cls.__index_docs(docs, index_name=index_name)
            index_time = time.time() - start_time
            logger.info(f'索引文档耗时: {index_time:.2f}秒')
            
            logger.info(f'索引同步page: {page}, docs: {len(docs)},last_id:{last_id}')

            last_id = docs[-1].id
            page += 1

        cls._rotating(index_name)

    @classmethod
    def __get_alis_name(cls):
        return f'{ES_INDEX_PREFIX}-{cls._doc_model.get_name()}'

    @classmethod
    def __index_docs(cls, docs: list[BaseDocModel], index_name=None):
        index_name = index_name if index_name else cls.__get_alis_name()
        
        batch_size = 100
        for i in range(0, len(docs), batch_size):
            batch = docs[i:i + batch_size]
            body = ''
            for doc in batch:
                body += f'''{json.dumps(
                    {
                        'index': {
                            '_index': index_name,
                            '_id': doc.id
                        }
                    }
                )}
{doc.to_json()}
'''
            
            elasticsearch_ext.client.bulk(index=index_name, body=body, refresh=True)

        return True

    @classmethod
    def _rotating(cls, index_name: str):
        """
        轮转索引
        :param index_name:
        :return:
        """
        alias_name = cls.__get_alis_name()

        # 设置别名
        if not elasticsearch_ext.client.indices.exists_alias(name=alias_name):
            elasticsearch_ext.client.indices.put_alias(index=index_name, name=alias_name)
        else:
            # 替换别名指向
            old_indices = elasticsearch_ext.client.indices.get_alias(name=alias_name).keys()
            actions = [{"remove": {"index": old, "alias": alias_name}} for old in old_indices]
            actions.append({"add": {"index": index_name, "alias": alias_name}})
            elasticsearch_ext.client.indices.update_aliases({"actions": actions})

        index_names = list(elasticsearch_ext.client.indices.get_alias(f'{alias_name}*').keys())

        dated_indices = []
        for name in index_names:
            datetime = name.split('-')[-1]
            dt = arrow.get(datetime, "YYYY_MM_DD_HH_mm_ss")
            dated_indices.append((name, dt))

        # 按时间降序排序
        dated_indices.sort(key=lambda x: x[1], reverse=True)

        for index_name, _ in dated_indices[4:]:
            logger.info(f"Deleting old index: {index_name}")
            elasticsearch_ext.client.indices.delete(index=index_name, ignore=[400, 404])
        return True

    @classmethod
    def _next_docs_page(cls, last_id: int) -> list[BaseDocModel]:
        pass

    @classmethod
    def _to_docs(cls, ids: Optional[list] = None, entity_list: Optional[list] = None) -> list[BaseDocModel]:
        pass
