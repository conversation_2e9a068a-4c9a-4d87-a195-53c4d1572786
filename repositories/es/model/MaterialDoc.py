import arrow

from core.constant import STR_EMPTY
from core.func import is_vertical
from core.ext import oss_ext
from typing import Optional

from repositories.material_folder_tree import material_folder_tree
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from pydantic import BaseModel
from flask import has_app_context, current_app


class BaseDocModel(BaseModel):

    @staticmethod
    def get_mapping():
        return {
            'properties': {
            }
        }

    @staticmethod
    def get_name():
        return 'default'

    def to_json(self):
        return self.model_dump_json()


class MaterialDoc(BaseDocModel):
    # id
    id: int
    # 用户id
    user_id: int
    # 企业id
    organize_id: int
    # 地址
    url: str
    # 宽
    width: Optional[int]
    # 高
    height: Optional[int]
    # 低质量地址
    low_quality_url: str
    # 同步的来源id
    source_id: Optional[int]
    # 文件id
    file_id: int
    # 所在目录id
    folder_id: int
    # 目录名称
    folder_name: str
    # 旋转角度
    rotate: int
    # 是否竖屏
    vertical: Optional[bool]
    # 时长
    duration: int
    # 父级目录的所有id
    folder_ids: list[int]
    # 本地路径
    local_path: Optional[str]
    # 文件名
    file_name: str
    # 状态
    status: int
    # 素材类型
    type: Optional[int]
    # 标签
    tags: Optional[list[str]] = []
    # 创建时间
    create_time: int
    update_time: int

    @staticmethod
    def get_mapping():
        return {
            "properties": {
                "id": {"type": "long"},
            }
        }

    @staticmethod
    def get_name():
        if has_app_context():
            return current_app.config.get('MATERIAL_INDEX_NAME', 'material')
        return 'material'

    @staticmethod
    def parse(material: Material, video_file: VideoFileMetadata, folder_name: str) -> "MaterialDoc":
        return MaterialDoc(
            id=material.id,
            user_id=material.user_id,
            organize_id=material.organize_id,
            width=video_file.width,
            height=video_file.height,
            url=oss_ext.add_domain(video_file.key),
            low_quality_url=oss_ext.add_domain(video_file.low_quality_key) if video_file.low_quality_key else '',
            source_id=material.source_id,
            file_id=material.file_id,
            folder_id=material.folder_id,
            folder_ids=material_folder_tree.get_tree(material.user_id, material.organize_id).get_folder_path_ids(
                material.folder_id,
            ),
            vertical=is_vertical(video_file.width, video_file.height, video_file.rotate),
            local_path=material.local_path,
            folder_name=folder_name if folder_name else STR_EMPTY,
            file_name=material.file_name,
            duration=video_file.duration,
            rotate=video_file.rotate,
            type=material.type,
            status=video_file.state,
            create_time=arrow.get(material.create_time).int_timestamp * 1000,
            update_time=arrow.get(material.update_time).int_timestamp * 1000
        )
