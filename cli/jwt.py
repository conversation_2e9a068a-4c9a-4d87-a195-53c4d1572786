from datetime import timedelta

import click
from flask import current_app


@click.group(name='jwt')
def jwt_command():
    """Elasticsearch操作命令"""
    pass


@jwt_command.command('encode')
def ent_encode():
    """生成JWT"""
    with current_app.app_context():
        from flask_jwt_extended import create_access_token
        print(current_app.config.get('JWT_PUBLIC_KEY'))
        print(create_access_token(identity=314102675027268735, additional_claims={'oid': 29},expires_delta=timedelta(days=30)))
