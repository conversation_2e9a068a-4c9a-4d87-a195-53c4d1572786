from flask import Flask

from cli.db import generate_model_command
from cli.es import elasticsearch_command
from cli.jwt import jwt_command
from cli.milvus import milvus_command
from cli.history_cmd import register_history_command
from cli.util import util_command


def register_command(app: Flask):
    # 注册命令
    with app.app_context():
        app.cli.add_command(generate_model_command)
        app.cli.add_command(elasticsearch_command)
        app.cli.add_command(milvus_command)
        app.cli.add_command(jwt_command)
        app.cli.add_command(milvus_command)
        app.cli.add_command(util_command)
        register_history_command(app)