import logging
import click
from flask.cli import with_appcontext
from flask import current_app

from repositories.material_es_repository import MaterialEsRepository

logger = logging.getLogger(__name__)


@click.group(name='es')
def elasticsearch_command():
    """Elasticsearch操作命令"""
    pass


@elasticsearch_command.command('rebuild')
@with_appcontext
def init_index():
    """初始化Elasticsearch的素材索引"""
    with current_app.app_context():
        logger.info("创建索引")

        index_name = MaterialEsRepository.rebuild()

        logger.info('Elasticsearch index created: %s', index_name)
