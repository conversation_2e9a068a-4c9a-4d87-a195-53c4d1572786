import os
import click
from flask import current_app
from sqlalchemy.engine import create_engine
from sqlalchemy.schema import MetaData


@click.command('generate-model')
@click.argument('table_name')
def generate_model_command(table_name):
    """Generate SQLAlchemy model file for a specific table."""
    model_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'repositories/mysql/model')
    model_file = os.path.join(model_dir, f"{table_name}.py")

    if os.path.exists(model_file):
        if not click.confirm(f'Model file {model_file} already exists. Do you want to overwrite it?'):
            click.echo('Aborted.')
            return

    # Get database URL from Flask config
    db_url = current_app.config['SQLALCHEMY_DATABASE_URI']

    # Use reflection to fill in the metadata
    engine = create_engine(db_url)
    metadata = MetaData()
    metadata.reflect(engine, only=[table_name])

    if table_name not in metadata.tables:
        click.echo(f'Table {table_name} not found in database.')
        return

    # Import the generator class
    from sqlacodegen.generators import DeclarativeGenerator

    # Instantiate the generator
    generator = DeclarativeGenerator(metadata, engine, [])

    # Generate the model code
    model_code = generator.generate()

    # Write the generated model code to file
    with open(model_file, 'w') as f:
        f.write(model_code)

    click.echo(f'Generated model file: {model_file}')
