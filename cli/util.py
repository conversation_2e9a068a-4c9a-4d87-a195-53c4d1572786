
import click
from flask import current_app
from flask.cli import with_appcontext
from core.ext import oss_ext,encrypt_decrypter


@click.group(name='util')
def util_command():
    """Elasticsearch操作命令"""
    pass


@util_command.command('encode')
@click.option('--value', default=None)
@with_appcontext
def encode(value):
    """加密"""
    click.echo(f'''YK_ENC({encrypt_decrypter.encrypt(value)})''')


@util_command.command('decode')
@click.option('--value', default=None)
@with_appcontext
def decode(value):
    """解密"""
    click.echo(f'''{encrypt_decrypter.decrypt(value)}''')

@util_command.command('config')
@with_appcontext
def config():
    for k, v in current_app.config.items():
        print(f'{k}={v}')


@util_command.command('oss_sign')
@click.option('--url')
@with_appcontext
def oss_sign(url):
    with current_app.app_context():
        print(oss_ext.sign_url(url))
