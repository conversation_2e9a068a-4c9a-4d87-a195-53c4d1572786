import json
import logging

import arrow
import click
from flask import current_app
from flask.cli import with_appcontext
from sqlalchemy import select, func

from cli.history_cmd import history_cmd
from cli.history_cmd import _material_import_basic


from core.ext import oss_ext, db
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_ata import VideoFileAta

HISTORY_ATA_FILE_PREFIX = 'material/scene-ata/'

logger = logging.getLogger(__name__)


@history_cmd.command('rebuild-ata')
@click.option('--max-workers', default=4, help='Maximum number of worker threads')
@click.option('--batch-size', default=1000, help='Number of records to process in each batch')
@click.option('--last-id', default=0, help='Number of records to process in each batch')
@with_appcontext
def rebuild_ata(max_workers, batch_size, last_id):
    app = current_app._get_current_object()  # 在主线程中获取 app 对象

    def process_material(material: Material):
        try:
            with app.app_context():  # 在每个线程中创建新的应用上下文

                exist = db.session.execute(
                    select(func.count()).select_from(VideoFileAta).where(VideoFileAta.file_id == material.file_id)
                ).scalar_one_or_none() > 0
                if exist:
                    logger.info(f"已存在 {material.file_id} 的ATA数据")
                    return
                if oss_ext.client.object_exists(f'{HISTORY_ATA_FILE_PREFIX}{material.file_id}.json'):
                    data = oss_ext.client.get_object(f'{HISTORY_ATA_FILE_PREFIX}{material.file_id}.json').read().decode(
                        'utf-8')
                    if data is None or len(data) == 0:
                        # VoiceInsightTask.run(material.file_id, material.id)
                        return
                    try:
                        video_file_ata = VideoFileAta(
                            file_id=material.file_id,
                            utterances=json.dumps(json.loads(data).get('utterances'), ensure_ascii=False),
                            create_time=arrow.now().datetime
                        )

                        db.session.add(video_file_ata)
                        db.session.commit()
                        logger.info(f"成功处理 {material.file_id} 历史数据迁移")
                    except Exception as _:
                        pass
                        # VoiceInsightTask.run(material.file_id, material.id)
                else:
                    # VoiceInsightTask.run(material.file_id, material.id)
                    pass
        except Exception as e:
            logger.error(f"处理素材失败 material_id={material.id}: {e}")

    _material_import_basic(max_workers, batch_size, last_id, process_material, 4)
