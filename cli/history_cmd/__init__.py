import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

import click
from flask import current_app
from sqlalchemy import func, select, or_
from tqdm import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm

from core.ext import db
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_task import VideoFileTask

logger = logging.getLogger(__name__)


@click.group(name='history')
def history_cmd():
    """历史记录同步"""
    pass


def get_material_count(last_id, task_code):
    return db.session.execute(
        select(func.count()).select_from(Material).outerjoin(
            VideoFileTask, Material.file_id == VideoFileTask.file_id
        ).where(
            or_((VideoFileTask.task == task_code) & (VideoFileTask.state != 1), VideoFileTask.id == None),
            Material.id > last_id, Material.deleted == 0
        )
    ).scalar_one()


def get_material_list(last_id, batch_size,task_code):
    query = select(Material.id, Material.file_id).select_from(Material).outerjoin(
        VideoFileTask, Material.file_id == VideoFileTask.file_id
    ).where(
        or_((VideoFileTask.task == task_code) & (VideoFileTask.state != 1), VideoFileTask.id == None),
        Material.id > last_id, Material.deleted == 0
    ).order_by(Material.id.asc()).limit(batch_size)
    return db.session.execute(query).all()


def _material_import_basic(max_workers, batch_size, last_id, process_material,task_code):
    app = current_app._get_current_object()  # 在主线程中获取 app 对象

    total_count = get_material_count(last_id,task_code)

    pbar = tqdm(total=total_count, desc="处理素材文件", position=0, leave=True)

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 分批处理数据
        while True:
            # 查询一批数据
            with logging_redirect_tqdm():
                with app.app_context():
                    materials = get_material_list(last_id, batch_size, task_code)

                    # 如果没有更多数据，退出循环
                    if not materials:
                        break

                    # 提交这一批的任务
                    futures = [executor.submit(process_material, material) for material in materials]

                    # 等待当前批次完成
                    for future in as_completed(futures):
                        try:
                            future.result()
                        except Exception as e:
                            logger.error(f"处理任务时发生错误: {str(e)}")
                        finally:
                            pbar.update(1)

                    # 更新偏移量
                    last_id = materials[-1].id

                    # 清理当前批次的会话
                    db.session.remove()


def register_history_command(app):
    from . import import_folder, import_material, rebuild_vector, rebuild_ata, import_user_material
    app.cli.add_command(history_cmd)
