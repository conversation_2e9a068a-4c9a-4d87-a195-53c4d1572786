import logging
import traceback

import click
from flask.cli import with_appcontext
from flask import current_app

from tasks.material_handle_tasks.embedding_task import EmbeddingTask
from cli.history_cmd import history_cmd, _material_import_basic

logger = logging.getLogger(__name__)


@history_cmd.command('rebuild-vector')
@click.option('--max-workers', default=4, help='Maximum number of worker threads')
@click.option('--batch-size', default=1000, help='Number of records to process in each batch')
@click.option('--last-id', default=0, help='Number of records to process in each batch')
@with_appcontext
def rebuild_vector(max_workers, batch_size, last_id):
    """重建所有素材的向量索引"""
    app = current_app._get_current_object()  # 在主线程中获取 app 对象

    logger.info('开始重建向量索引')

    def process_material(material):
        try:
            with app.app_context():  # 在每个线程中创建新的应用上下文
                EmbeddingTask.SKIP_COMPRESSION = True
                EmbeddingTask.run(material.file_id, material.id)
        except Exception as _:
            logger.error(f"处理素材失败 material_id={material.id}: {traceback.format_exc()}")

    _material_import_basic(max_workers, batch_size, last_id, process_material,3)
