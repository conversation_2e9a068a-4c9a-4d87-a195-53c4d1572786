import json
import logging
import os.path
import subprocess
import csv
from fractions import Fraction
from concurrent.futures import ThreadPoolExecutor, as_completed

import arrow
import click
from flask.cli import with_appcontext
from flask import current_app
from sqlalchemy import select, func
from tqdm import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm

from core.ext import db, snowflake_ext, oss_ext
from repositories.mysql.model.material import Material
from repositories.mysql.model.material_folder import MaterialFolder
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from utils import url_util
from cli.history_cmd import history_cmd

logger = logging.getLogger(__name__)


@history_cmd.command('import-material')
@click.option('--file', default=None)
@click.option('--max-workers', default=4, help='Maximum number of worker threads')
@with_appcontext
def import_material(file, max_workers):
    app = current_app._get_current_object()  # Get the actual app object

    # First count total lines
    total_lines = sum(1 for _ in open(file, 'r'))

    # Create a progress bar
    pbar = tqdm(total=total_lines, desc="导入素材", position=0, leave=True)
    error_pbar = tqdm(total=0, desc="错误", position=1, leave=True, bar_format='{desc}: {n}')

    # Read all lines using csv module
    with open(file, 'r', newline='') as f:
        csv_reader = csv.reader(f, quotechar='"', quoting=csv.QUOTE_MINIMAL)
        lines = list(csv_reader)

    def process_line(line):
        # Create a new application context for each thread
        with app.app_context():
            try:
                if len(line) != 7:
                    error_pbar.set_description(f"错误: 行格式不正确 {line}")
                    error_pbar.update(1)
                    return False

                id, url, low_quality_url, src_path, category_ids, rotate, create_time = line
                with logging_redirect_tqdm():
                    success = sync_material(id, url, low_quality_url, src_path, category_ids, rotate, create_time)
                if not success:
                    error_pbar.set_description(f"错误: 处理失败 ID={id}")
                    error_pbar.update(1)
                return success
            except Exception as e:
                error_pbar.set_description(f"错误: {str(e)[:50]}...")
                error_pbar.update(1)
                return False
            finally:
                pbar.update(1)

    # Process files using thread pool
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_line = {executor.submit(process_line, line): line for line in lines}

        # Wait for all tasks to complete
        for future in as_completed(future_to_line):
            try:
                future.result()
            except Exception as e:
                error_pbar.set_description(f"错误: {str(e)[:50]}...")
                error_pbar.update(1)

    pbar.close()
    error_pbar.close()


def __get_video_info(url):
    try:
        cmd = f'''ffprobe -v error -select_streams v -show_streams -of json "{url}" '''

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode != 0:
            logger.error(f"ffprobe failed with error: {result.stderr}")
            return None

        info = json.loads(result.stdout)
        streams = info.get('streams', [])

        if not streams:
            logger.warning(f"No video streams found for URL: {url}")
            return None

        return streams[0]
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse ffprobe output: {e}")
        return None
    except Exception as e:
        logger.error(f"Error getting video info for {url}: {e}")
        return None


def sync_material(id, url, low_quality_url, src_path, category_ids, rotate, create_time):
    try:
        logger.info(f'开始处理:id{id} ,url:{url}')

        exist = db.session.execute(
            select(func.count()).select_from(VideoFileMetadata).where(VideoFileMetadata.id == id)
        ).scalar_one() > 0

        sign_url = oss_ext.add_domain(url_util.get_key(url), True)
        video_info = __get_video_info(sign_url)

        if not video_info:
            logger.error(f"无法获取视频信息，跳过处理: {id}")
            return False

        # 写入file_metadata
        video_file = VideoFileMetadata(
            id=id,
            key=url_util.get_key(url),
            low_quality_key=url_util.get_key(low_quality_url),
            rotate=int(rotate),
            state=1,
            width=video_info.get('width', 0),
            height=video_info.get('height', 0),
            duration=int(float(Fraction(video_info.get('time_base', '0/1')) * video_info.get('duration_ts', 0)) * 1000),
            create_time=arrow.get(create_time).datetime
        )

        if not exist:
            db.session.add(video_file)

        try:
            folder_ids = json.loads(category_ids)
        except json.JSONDecodeError:
            logger.error(f"Invalid category_ids format: {category_ids}")
            return False

        folder_query = select(MaterialFolder).filter(MaterialFolder.id.in_(folder_ids))
        folder_list: list[MaterialFolder] = db.session.execute(folder_query).scalars().all()

        for folder in folder_list:
            query = select(func.count()).select_from(Material).where(Material.file_id == id,
                                                                     Material.folder_id == folder.id)
            exist = db.session.execute(query).scalar_one() > 0
            if exist:
                continue
            material = Material(
                id=snowflake_ext.next_id(),
                user_id=0,
                organize_id=folder.organize_id,
                source_id=0,
                file_id=id,
                folder_id=folder.id,
                create_time=arrow.get(create_time).datetime
            )
            if src_path and len(src_path) and src_path != '""':
                material.local_path = os.path.dirname(src_path)
                material.file_name = os.path.basename(src_path)

            db.session.add(material)

        db.session.commit()
        logger.info(f'处理完成:id{id} ,url:{url}')
        return True

    except Exception as e:
        logger.error(f"处理素材时发生错误 id:{id}, error: {str(e)}")
        db.session.rollback()
        return False
