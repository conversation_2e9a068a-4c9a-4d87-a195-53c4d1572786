import logging

import arrow
import click
from anytree import PreOrderIter
from flask.cli import with_appcontext
from flask import current_app
from sqlalchemy import select, func, update
from tqdm import tqdm

from core.ext import db
from repositories.material_folder_tree import material_folder_tree
from repositories.mysql.model.material_folder import MaterialFolder
from . import history_cmd

logger = logging.getLogger(__name__)


@history_cmd.command('import-folder')
@click.option('--file', default=None)
@with_appcontext
def import_folder(file):
    """导入目录信息 /Users/<USER>/Desktop/material-history/crm_ai_yk_video_material.csv"""
    with current_app.app_context():
        # First count total lines for progress bar
        total_lines = sum(1 for _ in open(file, 'r'))

        with open(file, 'r') as f:
            for line in tqdm(f, total=total_lines, desc="导入目录"):
                line = line.strip()
                if not line:
                    continue
                id, organize_id, name, parent_id, create_time = line.split(',')
                query = select(func.count()).select_from(MaterialFolder).where(MaterialFolder.id == id)
                if db.session.execute(query).scalar_one():
                    continue
                material_folder = MaterialFolder(
                    id=id,
                    pid=parent_id,
                    user_id=0,
                    organize_id=organize_id,
                    path='',
                    name=name,
                    create_time=arrow.get(create_time).datetime
                )
                db.session.add(material_folder)
                db.session.commit()

        query = select(MaterialFolder.organize_id).select_from(MaterialFolder).group_by(MaterialFolder.organize_id)

        organize_ids = list(db.session.execute(query).scalars())
        for oid in tqdm(organize_ids, desc="更新目录路径"):
            folder_tree = material_folder_tree.get_tree(0, oid)
            for sub_node in PreOrderIter(folder_tree.get_node()):
                update_sql = update(MaterialFolder).where(
                    MaterialFolder.id == sub_node.id
                ).values(path="/".join(node.name for node in sub_node.path if node.id != 0))
                db.session.execute(update_sql)
            db.session.commit()
            logger.info(f"更新企业{oid}的目录路径成功")
