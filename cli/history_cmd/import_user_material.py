import csv
import json
import logging
import subprocess
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed
from fractions import Fraction

import arrow
import click
import requests
from flask import current_app
from flask.cli import with_appcontext
from oss2.models import PutObjectResult
from sqlalchemy import select
from tqdm import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm

from cli.history_cmd import history_cmd
from core.ext import snowflake_ext, oss_ext, db
from repositories.mysql.model.material import Material
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from utils import url_util

logger = logging.getLogger(__name__)


@history_cmd.command('import-user-material')
@click.option('--file', default=None)
@click.option('--max-workers', default=4, help='Maximum number of worker threads')
@with_appcontext
def import_user_material(file, max_workers):
    app = current_app._get_current_object()  # Get the actual app object

    # First count total lines
    total_lines = sum(1 for _ in open(file, 'r'))

    # Create a progress bar
    pbar = tqdm(total=total_lines, desc="导入用户素材", position=0, leave=True)

    with open(file, 'r', newline='') as f:
        csv_reader = csv.reader(f, quotechar='"', quoting=csv.QUOTE_MINIMAL)
        lines = list(csv_reader)

    def process_line(line):
        # Create a new application context for each thread
        with app.app_context():
            with logging_redirect_tqdm():
                id, user_id, organize_id, url, rotate, create_time = line

                _copy_static_to_current_bucket(url)

                video_key = url_util.get_key(url)
                logger.info(f'处理视频文件:{video_key}')

                exist_video_file = _is_user_file_exist(video_key)
                if exist_video_file is None:
                    sign_url = oss_ext.add_domain(video_key, True)
                    video_info = __get_video_info(sign_url)

                    video_file = VideoFileMetadata(
                        id=id,
                        key=video_key,
                        low_quality_key='',
                        rotate=int(rotate),
                        state=2,
                        width=video_info.get('width', 0),
                        height=video_info.get('height', 0),
                        duration=int(float(
                            Fraction(video_info.get('time_base', '0/1')) * video_info.get('duration_ts', 0)) * 1000),
                        create_time=arrow.get(create_time).datetime
                    )
                    db.session.add(video_file)
                else:
                    video_file = exist_video_file

                material = Material(
                    id=snowflake_ext.next_id(),
                    user_id=user_id,
                    organize_id=organize_id,
                    source_id=0,
                    file_id=video_file.id,
                    folder_id=0,
                    type=1,
                    create_time=arrow.get(create_time).datetime
                )
                db.session.add(material)

                db.session.commit()

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_line = {executor.submit(process_line, line): line for line in lines}

        for future in as_completed(future_to_line):
            try:
                future.result()
            except Exception as e:
                pbar.update(1)
                logger.info(f"Error processing line: {traceback.print_exc()}")


def __get_video_info(url):
    try:
        cmd = f'''ffprobe -v error -select_streams v -show_streams -of json "{url}" '''

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode != 0:
            logger.error(f"ffprobe failed with error: {result.stderr}")
            return None

        info = json.loads(result.stdout)
        streams = info.get('streams', [])

        if not streams:
            logger.warning(f"No video streams found for URL: {url}")
            return None

        return streams[0]
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse ffprobe output: {e}")
        return None
    except Exception as e:
        logger.error(f"Error getting video info for {url}: {e}")
        return None


def _is_user_file_exist(key):
    exist_query = select(VideoFileMetadata).where(VideoFileMetadata.key == key)

    return db.session.execute(exist_query).scalar_one_or_none()


def _copy_static_to_current_bucket(url):
    if 'static.inngke.com' in url:
        logger.info(f'下载旧文件:{url}')
        response = requests.get(url)
        logger.info(response.status_code)
        if response.status_code == 200:
            res: PutObjectResult = oss_ext.client.put_object(url_util.get_key(url), response.content)
            print(res.resp)
        else:
            logger.info("copy data error")
