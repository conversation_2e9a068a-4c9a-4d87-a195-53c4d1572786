import click
from flask import current_app
from flask.cli import with_appcontext

import logging

from pymilvus import DataType
from pymilvus.milvus_client import IndexParams

from core.ext import milvus_ext

logger = logging.getLogger(__name__)


@click.group(name='milvus')
def milvus_command():
    """Elasticsearch操作命令"""
    pass


@milvus_command.command('init')
@with_appcontext
def init_index():
    """初始化Milvus的素材索引"""
    with current_app.app_context():
        logger.info("创建索引")
        res = milvus_ext.client.has_collection('material_vector', timeout=10)
        if res:
            click.echo('创建成功')
            return

        schema = milvus_ext.client.create_schema(auto_id=True, enable_dynamic_field=True)
        schema.add_field('id', DataType.INT64, is_primary=True)
        schema.add_field('user_id', DataType.INT64, description='用户id')
        schema.add_field('organize_id', DataType.INT64, description='企业id')
        schema.add_field('file_id', DataType.INT64, description='文件id')
        schema.add_field('material_id', DataType.INT64, description='素材id')
        schema.add_field('frame_vector', DataType.FLOAT_VECTOR, dim=1024, description='帧向量')
        schema.add_field('text_vector', DataType.FLOAT_VECTOR, dim=1024, description='文本向量')
        schema.add_field('folder_ids', DataType.ARRAY, max_capacity=256, element_type=DataType.INT64,
                         description='文件夹id列表')
        schema.add_field('vertical', DataType.BOOL, description='是否竖屏')
        schema.add_field('clip_start', DataType.INT32, description='剪辑开始时间')
        schema.add_field('clip_duration', DataType.INT32, description='剪辑时长')
        schema.add_field('duration', DataType.INT32, description='视频时长')
        schema.add_field('tags', DataType.ARRAY, max_length=64, max_capacity=256, element_type=DataType.VARCHAR,
                         description='标签列表')
        schema.add_field('create_time', DataType.INT64, description='创建时间')

        milvus_ext.client.create_collection('material_vector', schema=schema)

        index_params = IndexParams()
        index_params.add_index(field_name='frame_vector', index_type='FLAT', metric_type="COSINE")
        index_params.add_index(field_name='text_vector', index_type='FLAT', metric_type="COSINE")

        milvus_ext.client.create_index('material_vector', index_params=index_params)

        click.echo('创建成功')
