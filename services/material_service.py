import os
import time
from datetime import datetime
import logging

from core.func import get_user_identity
from core.decorators import lock, transactional
from core.exception.ServiceException import ServiceException
from repositories.material_es_repository import material_es_repository
from repositories.material_folder_repository import material_folder_repository
from repositories.material_repository import material_repository
from repositories.material_vector_repository import material_vector_repository
from repositories.mysql.model.video_file_metadata import VideoFileMetadata
from repositories.video_file_repository import video_file_repository
from tasks import compress, voice_insight
from tasks import llm_insight
from utils import async_util, url_util

logger = logging.getLogger(__name__)


class MaterialService:

    @classmethod
    @lock(['url', 'key'])
    def add_material(cls, url: str = None, key: str = None, path: str = None,
                     fileName: str = None,
                     is_client: bool = False, folder_id=None, **kwargs):
        cid, oid = get_user_identity()

        url, key, path, fileName = cls._fix_params(url, key, path, fileName)

        video_file = VideoFileMetadata(key=key, state=0, create_time=datetime.now())

        # 获取或创建文件夹
        folder = None
        if folder_id:
            folder = material_folder_repository.get_by_id(folder_id)

        if folder is None:
            for i in range(1, 4):
                try:
                    folder = material_folder_repository.get_folder_or_create(
                        path=path,
                        user_id=cid,
                        organize_id=oid,
                        is_client=is_client
                    )
                    break
                except Exception as _:
                    logger.info(f'第「{i}」次创建文件目录发生异常等待0.5s')
                    time.sleep(0.5)

        if not folder:
            logger.info(f'文件夹: {path} 创建失败')
            raise ServiceException('文件夹创建失败')

        # 添加素材
        material = material_repository.add_material(
            folder, video_file, fileName, material_es_repository.build_by_id
        )
        if not material:
            logger.info(f'素材: {fileName} 添加失败')
            raise Exception('素材添加失败')

        # 压缩
        compress.delay(material.file_id, material.id)

        # 大模型理解
        llm_insight.delay(material.file_id, material.id)

        # 语音识别
        voice_insight.delay(material.file_id, material.id)

        logger.info(f'素材: {material.to_json()} 添加成功')

        return material.to_dict()

    def add_material_batch(self, urls: list[str], folder_id: int, **kwargs):
        for url in urls:
            self.add_material(url=url, folder_id=folder_id)

    @classmethod
    @transactional
    def edit_material(cls, id: int, fileName: str = None, **kwargs):
        material = material_repository.get_by_id(id)
        if not material:
            raise ServiceException('素材不存在')

        if fileName is not None and fileName != material.file_name:
            material_repository.edit_material(id, fileName)

            # 更新es
            material_es_repository.build_by_id(id)

        return True

    @classmethod
    @transactional
    def rotate(cls, rotateMaterialList: list[dict]):
        material_ids = [material['id'] for material in rotateMaterialList]

        rotate_material_map = {int(material['id']): material for material in rotateMaterialList}

        material_list = material_repository.get_material_by_ids(material_ids)

        material_id_map = {material.id: material for material in material_list}

        rotate_file_list = [{
            'id': material_id_map[int(rotate_material['id'])].file_id,
            'rotate': rotate_material['rotate']
        } for rotate_material in rotateMaterialList if material_id_map.get(int(rotate_material['id']))]

        # 更新数据库
        success_file_ids = video_file_repository.rotate_file(rotate_file_list)

        success_rotate_material_list = []
        for material in material_list:
            if material.file_id in success_file_ids:
                success_rotate_material_list.append({**rotate_material_map.get(material.id), 'errMsg': ''})

        # 更新es
        cls._async_es_milvus(ids=[material['id'] for material in success_rotate_material_list])

        return success_rotate_material_list

    @classmethod
    @transactional
    def move(cls, ids: list[int], folderId: int):
        folders = material_folder_repository.get_by_ids([folderId])
        cid, oid = get_user_identity()

        if not folders or len(folders) == 0:
            raise ServiceException('文件夹不存在')

        folder = folders[0]
        if folder.user_id != cid or folder.organize_id != oid:
            raise ServiceException('无法移动到该文件夹')

        row_count = material_repository.move_material(ids, folderId)
        if row_count <= 0:
            raise ServiceException('移动失败')

        # 更新es
        cls._async_es_milvus(ids)

        return True

    @classmethod
    def _async_es_milvus(cls, ids):
        material_es_repository.build_by_ids(ids)

        material_vector_repository.rebuild_by_material_ids(ids)

    @classmethod
    @transactional
    def remove(cls, ids: list[int]):
        row_count = material_repository.remove(ids)
        if row_count <= 0:
            raise ServiceException('删除失败')

        material_es_repository.remove_docs(ids)

        material_vector_repository.remove_vector(ids)

        return True

    @classmethod
    def get_by_ids(cls, ids: list[int]):
        material_list = material_repository.get_material_by_ids(ids)

        return [material.to_dict() for material in material_list]

    @classmethod
    def _fix_params(cls, url, key, path, fileName):
        if url:
            key = url_util.get_key(url)

        if path:
            _, ext = os.path.splitext(path)

            # 判断是否有后缀名,有后缀名则取出文件名
            if ext:
                fileName = os.path.basename(path)
                path = os.path.dirname(path)

        return url, key, path, fileName


material_service = MaterialService()
