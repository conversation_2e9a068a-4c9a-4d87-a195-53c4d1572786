import json
import logging

import arrow

from core.constant import CACHE_PREFIX
from core.ext import redis_client

logger = logging.getLogger(__name__)


class MaterialUsageAmountService:
    """
    素材使用量统计服务
    """

    # Lua 脚本：原子性地更新素材使用量
    LUA_INCR_SCRIPT = """
    local key = KEYS[1]
    local material_id = ARGV[1]
    local start = tonumber(ARGV[2])
    local end_pos = tonumber(ARGV[3])
    
    -- 获取当前值
    local current = redis.call('hget', key, material_id)
    local usage_array
    
    if not current then
        -- 如果不存在，创建新数组
        usage_array = {}
        for i = 1, end_pos + 1 do
            usage_array[i] = 0
        end
    else
        -- 解析现有数组
        usage_array = cjson.decode(current)
        
        -- 如果数组长度不够，扩展它
        local current_len = #usage_array
        if current_len < end_pos + 1 then
            for i = current_len + 1, end_pos + 1 do
                usage_array[i] = 0
            end
        end
    end
    
    -- 增加使用量（Lua 数组是从1开始的，所以要+1）
    for i = start + 1, end_pos + 1 do
        usage_array[i] = usage_array[i] + 1
    end
    
    -- 保存回 Redis
    redis.call('hset', key, material_id, cjson.encode(usage_array))
    
    return true
    """

    # 注册 Lua 脚本
    _lua_script = None

    @classmethod
    def _get_lua_script(cls):
        """获取已注册的 Lua 脚本"""
        if cls._lua_script is None:
            cls._lua_script = redis_client.register_script(cls.LUA_INCR_SCRIPT)
        return cls._lua_script

    @classmethod
    def incr(cls, material_fragment_list):
        """
        增加素材使用量
        :param material_fragment_list: [{materialId:int,start:int,end:int}]
        """

        logger.info(f'统计素材使用量：{material_fragment_list}')

        today_key = cls._get_today_key()
        lua_script = cls._get_lua_script()

        for material_fragment in material_fragment_list:
            material_id = material_fragment.get('materialId')
            start = material_fragment.get('start')
            end = material_fragment.get('end')

            try:
                # 使用 Lua 脚本原子性地执行更新
                lua_script(
                    keys=[today_key],
                    args=[str(material_id), start, end]
                )
            except Exception as e:
                logger.error(f'更新素材 {material_id} 使用量失败: {e}')

        return True

    @classmethod
    def merge_day_usage(cls, days: int):
        """
        合并多天的使用量数据
        :param days: 要合并的天数
        """
        merge_usage = {}
        max_length = {}  # 记录每个素材的最大数组长度

        days = int(days)

        for i in range(days):
            day = arrow.now().shift(days=-i).format("M-D")
            day_key = cls._get_key(day)
            logger.info(f'合并数据 key:{day_key}')
            
            try:
                usage_data = redis_client.hgetall(day_key)
                
                for material_id, usage_str in usage_data.items():
                    # 处理可能的 bytes 类型
                    if isinstance(material_id, bytes):
                        material_id = material_id.decode('utf-8')
                    if isinstance(usage_str, bytes):
                        usage_str = usage_str.decode('utf-8')
                    
                    try:
                        usage_array = json.loads(usage_str)
                        
                        # 记录最大长度
                        if material_id not in max_length:
                            max_length[material_id] = len(usage_array)
                        else:
                            max_length[material_id] = max(max_length[material_id], len(usage_array))
                        
                        # 初始化或扩展 merge_usage
                        if material_id not in merge_usage:
                            merge_usage[material_id] = [0] * len(usage_array)
                        else:
                            # 如果当前数组更长，扩展 merge_usage
                            if len(usage_array) > len(merge_usage[material_id]):
                                merge_usage[material_id].extend([0] * (len(usage_array) - len(merge_usage[material_id])))
                        
                        # 合并数据
                        for idx, value in enumerate(usage_array):
                            merge_usage[material_id][idx] += value
                            
                    except json.JSONDecodeError as e:
                        logger.error(f'解析素材 {material_id} 的使用数据失败: {e}')
                        continue
                        
            except Exception as e:
                logger.error(f'读取 {day_key} 数据失败: {e}')
                continue

        merge_key = cls._get_key(days)
        
        logger.info(f'保存合并数据到 key: {merge_key}')

        # 保存合并后的数据到 Redis
        for material_id, usage_array in merge_usage.items():
            try:
                redis_client.hset(merge_key, material_id, json.dumps(usage_array))
            except Exception as e:
                logger.error(f'保存素材 {material_id} 的合并数据失败: {e}')

    @classmethod
    def get_usage_by_day(cls, material_ids, day):
        statistics_key = cls._get_key(day)

        # 从 Redis 获取数据
        usage_data = redis_client.hmget(statistics_key, *material_ids)

        # 解析数据
        usage_map = {}

        for material_id, usage_str in zip(material_ids, usage_data):
            if usage_str:
                usage_map[material_id] = json.loads(usage_str)
            else:
                usage_map[material_id] = []

        return usage_map

    @classmethod
    def _get_today_key(cls):
        return cls._get_key(arrow.now().format("M-D"))

    @classmethod
    def _get_key(cls, day):
        return f'{CACHE_PREFIX}:material_usage:{day}'


# 初始化
material_usage_amount_service = MaterialUsageAmountService()
