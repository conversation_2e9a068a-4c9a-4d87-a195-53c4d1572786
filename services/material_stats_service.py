from typing import Optional

from repositories.material_es_repository import material_es_repository

import logging

import json

logger = logging.getLogger(__name__)


class MaterialStatsService:

    @staticmethod
    def get_folder_material_count(user_id: Optional[int] = None, organize_id: Optional[int] = None,
                                  folder_ids: Optional[list[int]] = None):
        if folder_ids:
            must = [{"terms": {"folder_ids": folder_ids}}]
        else:
            must = [
                {"term": {"user_id": user_id}},
                {"term": {"organize_id": organize_id}},
            ]

        query = {
            "size": 0,
            "query": {
                "bool": {
                    "must": must
                }
            },
            "aggs": {
                "folder_count": {
                    "terms": {
                        "field": "folder_ids",
                    }
                }
            }
        }

        logger.info(f'统计文件夹下文件数量：{json.dumps(query,ensure_ascii=False)}')

        response = material_es_repository.base_search(query)

        buckets = response.get('aggregations', {}).get('folder_count', {}).get('buckets', [])

        return {
            bucket.get('key'): bucket.get('doc_count')
            for bucket in buckets
        }


material_stats_service = MaterialStatsService()
