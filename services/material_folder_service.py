import logging
from typing import Optional

from anytree import Node, PreOrderIter

from core.func import get_user_identity
from core.decorators import transactional, lock
from core.exception.ServiceException import ServiceException
from repositories.material_es_repository import material_es_repository
from repositories.material_folder_repository import material_folder_repository
from repositories.material_folder_tree import material_folder_tree
from repositories.material_vector_repository import material_vector_repository
from repositories.mysql.model.material_folder import EntityType, SpecialFolderId
from services.material_stats_service import material_stats_service
from utils import async_util

logger = logging.getLogger(__name__)


class MaterialFolderService:

    @classmethod
    def add(cls, name: str, pid: int, sort: int = 0, **kwargs):
        cid, oid = get_user_identity()
        if pid is None:
            raise ServiceException('父文件夹不存在')

        if type(pid) is str:
            pid = int(pid)

        if pid > 0:
            parent = material_folder_repository.get_by_id(pid)
            if not parent:
                raise ServiceException('父文件夹不存在')
            # 企业的目录
            if parent.user_id == 0:
                cid = 0
        else:
            # 企业根目录
            if pid == SpecialFolderId.ENTERPRISE.value:
                cid = 0
            pid = 0

        # 获取树
        folder_tree = material_folder_tree.get_tree(cid, oid)

        path = folder_tree.get_path_by_id(pid) + "/" + name

        folder = material_folder_repository.get_folder_or_create(path=path, user_id=cid, organize_id=oid, sort=sort)

        if not folder:
            raise ServiceException('创建失败')

        return folder.id

    @classmethod
    def get_tree(cls, ids: Optional[str] = None, entityType: Optional[int] = None, **kwargs):
        if ids:
            ids = [int(id) for id in ids.split(',')]

        if isinstance(entityType, int):
            entity_type = entityType
        else:
            entity_type = int(entityType) if isinstance(entityType, str) and len(entityType) else None

        result_node = cls._get_result_node(entity_type)
        cid, oid = get_user_identity()

        linked_ids = material_folder_repository.get_linked_ids(cid)

        for root_node in result_node:
            folder_tree = material_folder_tree.get_tree(root_node.cid, root_node.oid)

            material_count_stats = material_stats_service.get_folder_material_count(
                user_id=root_node.cid, organize_id=root_node.oid, folder_ids=ids
            )

            if ids:
                # 迭代所有的节点
                for sub_node in folder_tree.pre_order_iter():
                    ancestor_ids = [ancestor.id for ancestor in sub_node.ancestors if ancestor.id in ids]
                    # 如果当前节点有父节点在筛选条件中跳过
                    if ancestor_ids:
                        ids.remove(sub_node.id)
                        continue
                    # 如果当前节点在筛选条件中，将当前节点的父节点设置为结果节点
                    if sub_node.id in ids:
                        ids.remove(sub_node.id)
                        sub_node.parent = root_node
            else:
                for c in folder_tree.get_node().children:
                    c.parent = root_node

            for sub_node in PreOrderIter(root_node):
                sub_node.link_folder_id = linked_ids.get(sub_node.id) if linked_ids.get(sub_node.id) else None
                sub_node.material_count = material_count_stats.get(sub_node.id) if material_count_stats.get(
                    sub_node.id) else 0

        res = [material_folder_tree.get_dict(node) for node in result_node]

        return res

    @classmethod
    @lock(['id'])
    @transactional
    def edit_folder(cls, id: int, pid: Optional[int] = None, name: Optional[str] = None, sort: Optional[int] = None,
                    **kwargs):
        folder = material_folder_repository.get_by_id(id)
        if not folder:
            raise ServiceException('文件夹不存在')

        if folder.is_client:
            raise ServiceException('客户端文件夹不允许编辑')

        update_fields = {}
        sync_es_milvus_flag = False
        # 传递了pid并且pid不等于当前数据库的pid
        if pid is not None and pid != folder.pid:
            sync_es_milvus_flag = True
            parent_folder = material_folder_repository.get_by_id(pid)
            if not parent_folder:
                raise ServiceException('父文件夹不存在')
            folder.pid = pid
            update_fields['pid'] = pid

        if name is not None and name != folder.name:
            if material_folder_repository.check_children_name_unique(folder.pid, name):
                raise ServiceException('同级文件夹下已存在同名文件夹')

            update_fields['name'] = name

        if sort is not  None:
            update_fields['sort'] = sort
        material_folder_repository.update_folder(folder.id, update_fields)

        cid, oid = get_user_identity()

        material_folder_tree.remove_tree(cid, oid)

        if sync_es_milvus_flag:
            async_util.run(cls._sync_es_milvus, folder_id=id)

        return id

    @classmethod
    def _sync_es_milvus(cls, folder_id,**kwargs):
        material_doc_list = material_es_repository.get_by_folder_id(folder_id)

        material_ids = [material_doc['id'] for material_doc in material_doc_list]

        logger.info(f'同步es数据: {material_ids}')
        material_es_repository.build_by_ids(material_ids)

        logger.info(f'同步milvus数据: {material_ids}')
        material_vector_repository.rebuild_by_material_ids(material_ids)

    @classmethod
    @lock(['sourceId', 'targetId'])
    @transactional
    def link_organize(cls, sourceId: int, targetId: int,**kwargs):
        """
        关联企业目录
        :param sourceId:
        :param targetId:
        :return:
        """
        cid, oid = get_user_identity()

        if material_folder_repository.link_organize_exist(sourceId, targetId):
            raise ServiceException('已存在关联')

        res = material_folder_repository.link_organize(sourceId, targetId, cid)

        return res

    @classmethod
    def _get_result_node(cls, entity_type: int):
        cid, oid = get_user_identity()

        if entity_type == EntityType.PERSONAL.value:
            return [Node(name='个人', cid=cid, oid=oid, id='-1')]
        if entity_type == EntityType.ENTERPRISE.value:
            return [Node(name='企业', cid=0, oid=oid, id='-2')]

        return [
            Node(name='个人', cid=cid, oid=oid, id='-1'),
            Node(name='企业', cid=0, oid=oid, id='-2')
        ]
