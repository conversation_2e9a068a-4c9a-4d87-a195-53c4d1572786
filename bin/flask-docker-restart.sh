#!/bin/bash

# 获取脚本所在目录的上一级目录（项目根目录）
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
cd "$PROJECT_ROOT"

export APP_ROOT=$PROJECT_ROOT

# Check if Docker Swarm is initialized
if ! docker info | grep -q "Swarm: active"; then
    echo "Docker Swarm is not initialized. Initializing..."
    docker swarm init
fi

# Check if the service exists
if docker service ls | grep -q "material-ai-yk"; then
    echo "Updating existing service..."
    docker service update --image inngke-docker.pkg.coding.net/java/inngke-docker-hub/material_ai_yk:latest --force --with-registry-auth material-ai-yk_material_ai_yk

    # Clean up
    echo "Cleaning up containers and images..."
    docker container prune -f
    docker image prune -a -f
else
    echo "Service does not exist. Deploying stack..."
    docker stack deploy -c "$PROJECT_ROOT/docker-compose.yml" material-ai-yk
fi