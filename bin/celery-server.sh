#!/bin/bash

set -e  # 脚本出错立即退出

# 自动获取项目根目录（bin 上一层）
APP_DIR="$(cd "$(dirname "$0")/.." && pwd)"

LOG_FILE="$APP_DIR/celery.log"

cd "$APP_DIR"

echo "停止 Celery"

celery -A celery-app.app control shutdown || echo "Warning: Celery might not be running."

echo "拉取最新代码"
git pull

echo "安装新依赖"
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

echo "启动 Celery"
nohup celery -A celery-app.app worker --loglevel=INFO -P threads --concurrency=4 > "$LOG_FILE" 2>&1 &

nohup celery -A celery-app.app worker --loglevel=INFO -P threads --concurrency=3 -Q voice-insight> "$LOG_FILE" 2>&1 &

echo "Celery 启动成功，日志路径: $LOG_FILE"