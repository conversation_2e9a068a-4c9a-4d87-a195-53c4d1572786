from core.constant import LOCK_PREFIX
from core.ext import scheduler, redlock_ext
from services.material_usage_amount_service import material_usage_amount_service


@scheduler.task('cron', id='merge_material_usage_midnight', hour=0, minute=20)
def merge_material_usage_midnight():
    lock_key = f'{LOCK_PREFIX}:merge_material_usage'
    lock = redlock_ext.get_lock(lock_key, ttl=10 * 1000)
    if lock is None:
        return
    days_list = [7, 15, 30, 60, 90]
    for days in days_list:
        material_usage_amount_service.merge_day_usage(days)
