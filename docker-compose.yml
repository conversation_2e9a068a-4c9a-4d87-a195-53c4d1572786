version: '3.8'

services:
  material_ai_yk:
    image: inngke-docker.pkg.coding.net/java/inngke-docker-hub/material_ai_yk:latest
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        window: 30s
    ports:
      - "5001:5001"
    volumes:
      - ${APP_ROOT}/.env:/app/.env
      - ${APP_ROOT}/logs:/app/logs
      - /var/secret_key.b64:/app/secret_key.b64