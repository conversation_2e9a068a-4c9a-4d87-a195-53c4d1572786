from typing import Optional

from pydantic import BaseModel

from core.ext import oss_ext


class MaterialDTO(BaseModel):
    id: Optional[int] = None

    materialId: Optional[int] = None

    url: Optional[str] = None

    status: Optional[int] = None

    width: Optional[int] = None

    height: Optional[int] = None

    duration: Optional[int] = None

    rotate: Optional[int] = None

    folder_name: Optional[str] = None

    folder_id: Optional[int] = None

    file_name: Optional[str] = None

    create_time: Optional[int] = None

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        if "url" in data and data["url"]:
            data["url"] = oss_ext.replace_domain(data["url"])
        return data


class MaterialSegmentDTO(MaterialDTO):
    clip_start: Optional[int] = None

    clip_duration: Optional[int] = None

    score: Optional[float] = None

class WordDTO(BaseModel):
    text: str
    start_time: int
    end_time: int
    attribute: dict

class MaterialSegmentSubtitleDTO(MaterialSegmentDTO):
    """
    {
    "last_word":{
            "attribute": {
              "event": "speech",
              "speaker": "1"
            },
            "end_time": 45540,
            "start_time": 44920,
            "text": "询"
    },
    "sub_ata":[
        {
            "attribute": {
              "event": "speech",
              "speaker": "1"
            },
            "end_time": 44920,
            "start_time": 44700,
            "text": "你"
        },
    ]
    }
    """

    last_word: Optional[dict] = None

    sub_ata: Optional[dict] = None