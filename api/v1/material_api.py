import logging

from flask import Blueprint, request
from flask_jwt_extended import jwt_required
from webargs.flaskparser import use_args

from core.constant import STR_EMPTY
from core.decorators import auth_required
from services.material_search_service import material_search_service, MaterialEsSearchEngine, \
    MaterialVectorSearchEngine, MaterialLLmSearchEngine
from services.material_service import material_service
from services.material_usage_amount_service import material_usage_amount_service
from utils.common_util import response
from .schemas.material_schemas import (
    AddMaterialSchema, AddMaterialBatchSchema, RotateMaterialSchema,
    MoveMaterialSchema, RemoveMaterialSchema, EsSearchSchema,
    VectorQuerySchema, SubtitleMatchSchema, UsageAmountItemSchema, EditMaterialSchema
)

logger = logging.getLogger(__name__)

bp = Blueprint('material', __name__, url_prefix='/api/ai/material')


@bp.route(STR_EMPTY, methods=['POST'])
@jwt_required()
@use_args(AddMaterialSchema(), location="json")
def add_material(args):
    """
    添加素材
    :return:
    """
    return response(material_service.add_material(**args))


@bp.route(STR_EMPTY, methods=['PUT'])
@jwt_required()
@use_args(EditMaterialSchema(), location="json")
def edit_material(args):
    """
    修改素材
    :return:
    """
    return response(material_service.edit_material(**args))


@bp.route('/batch', methods=['POST'])
@jwt_required()
@use_args(AddMaterialBatchSchema(), location="json")
def add_material_batch(args):
    """
    批量添加素材
    :return:
    """
    return response(material_service.add_material_batch(**args))


@bp.route('/rotate', methods=['POST'])
@use_args(RotateMaterialSchema(), location="json")
def rebuild(args):
    """
    旋转素材
    :return:
    """
    return response(material_service.rotate(**args))


@bp.route('/move', methods=['POST'])
@use_args(MoveMaterialSchema(), location="json")
@jwt_required()
def move(args):
    """
    移动素材
    :return:
    """
    return response(material_service.move(**args))


@bp.route(STR_EMPTY, methods=['DELETE'])
@use_args(RemoveMaterialSchema(), location="json")
def remove(args):
    """
    删除素材
    :return:
    """
    return response(material_service.remove(**args))


@bp.route('/paging', methods=['POST'])
@auth_required(allow_internal=True)
@use_args(EsSearchSchema(), location="json")
def es_search(args):
    """
    获取素材列表
    :return:
    """
    return response(material_search_service.search(MaterialEsSearchEngine, **args))


@bp.route('/query', methods=['POST'])
@auth_required(allow_internal=True)
@use_args(VectorQuerySchema(), location="json")
def vector_query(args):
    """
    向量查询
    :return:
    """
    return response(material_search_service.search(MaterialVectorSearchEngine, **args))

@bp.route('/subtitle-match', methods=['POST'])
@use_args(SubtitleMatchSchema(), location="json")
def subtitle_match(args):
    """
    字幕匹配
    :return:
    """
    return response(material_search_service.search(MaterialLLmSearchEngine, **args))


@bp.route('/usage-amount/incr', methods=['POST'])
def usage_amount_incr():
    """
    增加素材使用量
    """
    from marshmallow import ValidationError

    # 获取请求数据
    data = request.get_json(silent=True) or []

    # 验证数据必须是列表
    if not isinstance(data, list):
        raise ValidationError('请求数据必须是数组格式')

    if len(data) == 0:
        raise ValidationError('数组不能为空')

    if len(data) > 1000:
        raise ValidationError('数组长度不能超过1000')

    # 验证列表中的每个项目
    schema = UsageAmountItemSchema()
    validated_data = []
    for i, item in enumerate(data):
        try:
            validated_item = schema.load(item)
            validated_data.append(validated_item)
        except ValidationError as e:
            raise ValidationError(f'第{i+1}项数据验证失败: {e.messages}')

    return response(material_usage_amount_service.incr(validated_data))


@bp.route('/usage-amount/merge/<days>', methods=['POST'])
def merge_day_usage(days):
    """
    获取素材使用量
    """
    return response(material_usage_amount_service.merge_day_usage(days))


@bp.route('/find/ids', methods=['GET'])
@auth_required(allow_internal=True)
def find_by_ids():
    """
    根据id获取素材
    """
    ids = request.args.getlist('ids', type=int)
    return response(material_service.get_by_ids(ids))
