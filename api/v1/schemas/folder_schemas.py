from marshmallow import fields, validate

from api.v1.schemas import Schema


class AddFolderSchema(Schema):
    """添加文件夹参数验证"""
    name = fields.Str(required=True, validate=validate.Length(min=1, max=128))
    pid = fields.Int(required=True)
    sort = fields.Int(load_default=0, validate=validate.Range(min=0))


class GetTreeSchema(Schema):
    """获取文件夹树参数验证"""
    ids = fields.Str(allow_none=True, validate=validate.Regexp(r'^(\d+,)*\d+$',
                     error='ids格式错误，应为逗号分隔的数字'))
    entityType = fields.Int(allow_none=True, validate=validate.OneOf([1, 2],
                           error='entityType只能为1或2'))


class EditFolderSchema(Schema):
    """编辑文件夹参数验证"""
    pid = fields.Int(allow_none=True)
    name = fields.Str(allow_none=True, validate=validate.Length(min=1, max=128))
    sort = fields.Int(allow_none=True, validate=validate.Range(min=0))


class LinkOrganizeSchema(Schema):
    """关联企业目录参数验证"""
    sourceId = fields.Int(required=True, 
                         error_messages={'required': '源目录ID不能为空'})
    targetId = fields.Int(required=True,
                         error_messages={'required': '目标目录ID不能为空'}) 