from marshmallow import Schema as SourceSchema, INCLUDE


class Schema(SourceSchema):
    class Meta:
        unknown = INCLUDE


from .folder_schemas import AddFolderSchema, GetTreeSchema, EditFolderSchema, LinkOrganizeSchema
from .material_schemas import (
    AddMaterialSchema, AddMaterialBatchSchema, RotateMaterialSchema,
    MoveMaterialSchema, RemoveMaterialSchema, EsSearchSchema,
    VectorQuerySchema, SubtitleMatchSchema
)

__all__ = [
    'AddFolderSchema',
    'GetTreeSchema',
    'EditFolderSchema',
    'LinkOrganizeSchema',
    'AddMaterialSchema',
    'AddMaterialBatchSchema',
    'RotateMaterialSchema',
    'MoveMaterialSchema',
    'RemoveMaterialSchema',
    'EsSearchSchema',
    'VectorQuerySchema',
    'SubtitleMatchSchema',
    'Schema'
]