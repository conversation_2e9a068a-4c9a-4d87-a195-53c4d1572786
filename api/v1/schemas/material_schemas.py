from marshmallow import fields, validate, validates_schema, ValidationError, INCLUDE

from api.v1.schemas import Schema


class AddMaterialSchema(Schema):
    """添加素材参数验证"""
    url = fields.Str(allow_none=True, validate=validate.URL(error='URL格式不正确'))
    key = fields.Str(allow_none=True, validate=validate.Length(min=1, max=500))
    path = fields.Str(allow_none=True, validate=validate.Length(min=1, max=500))
    fileName = fields.Str(allow_none=True, validate=validate.Length(min=1, max=255))
    isClient = fields.Bool(load_default=False)
    folderId = fields.Int(allow_none=True, validate=validate.Range(min=1))

    @validates_schema
    def validate_required_fields(self, data, **kwargs):
        """至少需要提供url或key中的一个"""
        if not data.get('url') and not data.get('key'):
            raise ValidationError('url或key至少需要提供一个')

class EditMaterialSchema(Schema):
    """编辑素材参数验证"""
    id = fields.Int(required=True, validate=validate.Range(min=1))
    fileName = fields.Str(allow_none=True, validate=validate.Length(min=1, max=255))


class AddMaterialBatchSchema(Schema):
    """批量添加素材参数验证"""
    urls = fields.List(
        fields.Str(validate=validate.URL(error='URL格式不正确')),
        required=True,
        validate=validate.Length(min=1, max=100, error='urls数量必须在1-100之间')
    )
    folder_id = fields.Int(required=True, validate=validate.Range(min=1))


class RotateMaterialItemSchema(Schema):
    """旋转素材项参数验证"""
    id = fields.Int(required=True, validate=validate.Range(min=1))
    rotate = fields.Int(required=True, validate=validate.OneOf([0, 90, 180, 270], error='rotate只能为0, 90, 180, 270'))


class RotateMaterialSchema(Schema):
    """旋转素材参数验证"""
    rotateMaterialList = fields.List(
        fields.Nested(RotateMaterialItemSchema),
        required=True,
        validate=validate.Length(min=1, max=100, error='rotate_material_list数量必须在1-100之间')
    )


class MoveMaterialSchema(Schema):
    """移动素材参数验证"""
    ids = fields.List(
        fields.Int(validate=validate.Range(min=1)),
        required=True,
        validate=validate.Length(min=1, max=1000, error='ids数量必须在1-1000之间')
    )
    folderId = fields.Int(required=True, validate=validate.Range(min=1, error='所选目录无效'))


class RemoveMaterialSchema(Schema):
    """删除素材参数验证"""
    ids = fields.List(
        fields.Int(validate=validate.Range(min=1)),
        required=True,
        validate=validate.Length(min=1, max=1000, error='ids数量必须在1-1000之间')
    )


class EsSearchSchema(Schema):
    """ES搜索素材参数验证"""
    ids = fields.List(
        fields.Int(validate=validate.Range(min=1)),
        allow_none=True,
        validate=validate.Length(max=1000, error='ids数量不能超过1000')
    )
    folderId = fields.Int(allow_none=True, validate=validate.Range(min=-2))
    folderIds = fields.List(
        fields.Int(validate=validate.Range(min=-2)),
        allow_none=True,
        validate=validate.Length(max=100, error='folderIds数量不能超过100')
    )
    vertical = fields.Int(allow_none=True)
    createTimeStart = fields.Str(allow_none=True)
    createTimeEnd = fields.Str(allow_none=True)
    pageNo = fields.Int(load_default=1, validate=validate.Range(min=1, max=1000))
    pageSize = fields.Int(load_default=20, validate=validate.Range(min=1, max=100))


class VectorQuerySchema(Schema):
    """向量查询参数验证"""
    duration = fields.Int(required=True, validate=validate.Range(min=1))
    image = fields.Str(allow_none=True, validate=validate.URL(error='image必须是有效的URL'))
    keyword = fields.Str(allow_none=True, validate=validate.Length(min=1, max=500))
    keywords = fields.List(
        fields.Str(validate=validate.Length(min=1, max=500)),
        allow_none=True,
        validate=validate.Length(max=10, error='keywords数量不能超过10')
    )
    excludedIds = fields.List(
        fields.Int(validate=validate.Range(min=1)),
        allow_none=True,
        validate=validate.Length(max=1000, error='excludedIds数量不能超过1000')
    )
    folderId = fields.Int(allow_none=True, validate=validate.Range(min=1))
    folderIds = fields.List(
        fields.Int(validate=validate.Range(min=-2)),
        allow_none=True,
        validate=validate.Length(max=100, error='folderIds数量不能超过100')
    )
    vertical = fields.Int(allow_none=True)
    createTimeStart = fields.Str(allow_none=True)
    createTimeEnd = fields.Str(allow_none=True)
    pageNo = fields.Int(load_default=1, validate=validate.Range(min=1, max=1000))
    pageSize = fields.Int(load_default=20, validate=validate.Range(min=1, max=100))

    @validates_schema
    def validate_search_params(self, data, **kwargs):
        """验证搜索参数：image、keyword、keywords至少需要提供一个"""
        image = data.get('image')
        keyword = data.get('keyword')
        keywords = data.get('keywords')
        
        if not image and not keyword and not keywords:
            raise ValidationError('image、keyword、keywords至少需要提供一个')


class SubtitleMatchSchema(Schema):
    """字幕匹配参数验证"""
    keyword = fields.Str(required=True, validate=validate.Length(min=1, max=500))
    materialIds = fields.List(
        fields.Int(validate=validate.Range(min=1)),
        required=True,
        validate=validate.Length(min=1, max=1000, error='materialIds数量必须在1-1000之间')
    )


class UsageAmountItemSchema(Schema):
    """使用量统计项参数验证"""
    materialId = fields.Int(required=True, validate=validate.Range(min=1))
    start = fields.Int(required=True, validate=validate.Range(min=0))
    end = fields.Int(required=True, validate=validate.Range(min=0))

    @validates_schema
    def validate_time_range(self, data, **kwargs):
        """验证时间范围：end必须大于start"""
        if data.get('end') <= data.get('start'):
            raise ValidationError('end必须大于start')


# 对于直接接收列表的情况，我们需要创建一个特殊的schema
# 这个将在API中特殊处理
class UsageAmountIncrListSchema(Schema):
    """增加使用量参数验证 - 用于验证列表中的每个项目"""
    pass
