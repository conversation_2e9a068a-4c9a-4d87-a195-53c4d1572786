import logging

from flask import Blueprint
from flask_jwt_extended import jwt_required
from webargs.flaskparser import use_args

from core.constant import STR_EMPTY
from services.material_folder_service import MaterialFolderService
from utils.common_util import response
from .schemas.folder_schemas import AddFolderSchema, GetTreeSchema, EditFolderSchema, LinkOrganizeSchema

logger = logging.getLogger(__name__)

bp = Blueprint('folder', __name__, url_prefix='/api/ai/folder')


@bp.route(STR_EMPTY, methods=['POST'])
@jwt_required()
@use_args(AddFolderSchema(), location="json")
def add(args):
    """
    添加素材
    :return:
    """
    return response(MaterialFolderService.add(**args))


@bp.route('/tree', methods=['GET'])
@jwt_required()
@use_args(GetTreeSchema(), location="querystring")
def get_tree(args):
    """
    获取文件夹树
    :return:
    """
    return response(MaterialFolderService.get_tree(**args))


@bp.route('/<int:id>', methods=['PUT'])
@jwt_required()
@use_args(EditFolderSchema(), location="json")
def edit_folder(args, id):
    """
    编辑目录
    :return:
    """
    args['id'] = id
    return response(MaterialFolderService.edit_folder(**args))


@bp.route('/link', methods=['POST'])
@jwt_required()
@use_args(LinkOrganizeSchema(), location="json")
def link_organize(args):
    """
    同步素材到企业
    :return:
    """
    return response(MaterialFolderService.link_organize(**args))
