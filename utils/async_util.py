from concurrent.futures import Thread<PERSON>oolExecutor

from flask import current_app

from core.trace import get_trace_id

default_pool = ThreadPoolExecutor(max_workers=20)


def run(func, pool: ThreadPoolExecutor = None, **kwargs):
    app = current_app._get_current_object()  # 拿到真实 app 对象

    trace_id = get_trace_id()

    def task_wrapper():
        with app.app_context():
            try:
                get_trace_id(trace_id)
                func(**kwargs)
            except Exception:
                app.logger.exception("异步发生错误")

    if pool is None:
        pool = default_pool

    future = pool.submit(task_wrapper)

    return future
