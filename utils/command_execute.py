import inspect
import json
import logging
import subprocess

from core.trace import get_trace_id
from core.ext import oss_ext

logger = logging.getLogger(__name__)


def execute(cmd: str, load: bool = False):
    logger.info(f'准备执行命令: {cmd}')

    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

    output = f'''
cmd: 
{cmd}

stdout:
{result.stdout}

stderr:
{result.stderr}
'''

    called = inspect.stack()[1].function

    __upload_output(output, called)

    if load:
        return json.loads(result.stdout)
    else:
        return


def __upload_output(output, called):
    trace_id = get_trace_id()

    output_name = f'tmp/{trace_id}/{called}.txt'

    logger.info(f"命令执行结果路径:{output_name}")

    oss_ext.client.put_object(output_name, output)
