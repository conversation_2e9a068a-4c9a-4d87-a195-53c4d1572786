import os
from urllib import parse

from utils import url_util


def parse_file_key(video_url):
    file_key = url_util.get_key(video_url)

    basename = os.path.basename(file_key)

    filename, file_suffix = get_video_name_suffix(video_url)

    low_quality_key = file_key.replace(basename, f'{filename}_low.{file_suffix}')

    source_file_bak_key = file_key.replace('/material/', '/source-material/')

    return file_key, source_file_bak_key, low_quality_key


def get_video_name_suffix(video_url):
    basename = os.path.basename(url_util.get_key(video_url))

    return basename.split('.')
