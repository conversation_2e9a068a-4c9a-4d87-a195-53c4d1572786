"""
内部调用工具类
用于在内部服务调用时，绕过JWT认证，直接传递用户身份信息
"""
import requests
from typing import Optional, Dict, Any


class InternalCallUtil:
    """内部调用工具类"""
    
    @staticmethod
    def make_request(
        url: str,
        method: str = 'GET',
        cid: Optional[int] = None,
        oid: Optional[int] = None,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> requests.Response:
        """
        发起内部调用请求
        
        :param url: 请求URL
        :param method: HTTP方法
        :param cid: 用户ID
        :param oid: 组织ID
        :param data: 请求体数据
        :param params: 查询参数
        :param headers: 额外的请求头
        :param kwargs: 其他requests参数
        :return: 响应对象
        """
        if headers is None:
            headers = {}
        
        # 添加内部调用标识头
        if cid is not None:
            headers['X-Internal-CID'] = str(cid)
        if oid is not None:
            headers['X-Internal-OID'] = str(oid)
        
        # 添加内容类型头
        if method.upper() in ['POST', 'PUT', 'PATCH'] and 'Content-Type' not in headers:
            headers['Content-Type'] = 'application/json'
        
        return requests.request(
            method=method,
            url=url,
            json=data,
            params=params,
            headers=headers,
            **kwargs
        )
    
    @staticmethod
    def get(url: str, cid: Optional[int] = None, oid: Optional[int] = None, **kwargs) -> requests.Response:
        """GET请求"""
        return InternalCallUtil.make_request(url, 'GET', cid, oid, **kwargs)
    
    @staticmethod
    def post(url: str, data: Optional[Dict[str, Any]] = None, cid: Optional[int] = None, oid: Optional[int] = None, **kwargs) -> requests.Response:
        """POST请求"""
        return InternalCallUtil.make_request(url, 'POST', cid, oid, data=data, **kwargs)
    
    @staticmethod
    def put(url: str, data: Optional[Dict[str, Any]] = None, cid: Optional[int] = None, oid: Optional[int] = None, **kwargs) -> requests.Response:
        """PUT请求"""
        return InternalCallUtil.make_request(url, 'PUT', cid, oid, data=data, **kwargs)
    
    @staticmethod
    def delete(url: str, cid: Optional[int] = None, oid: Optional[int] = None, **kwargs) -> requests.Response:
        """DELETE请求"""
        return InternalCallUtil.make_request(url, 'DELETE', cid, oid, **kwargs)


# 创建全局实例
internal_call = InternalCallUtil()
