import logging
import os
from concurrent.futures import ThreadPoolExecutor

import cv2

from utils import command_execute

logger = logging.getLogger(__name__)

def get_video_info(video_file):
    cmd = f'''ffprobe -v error -select_streams v -show_streams -of json '{video_file}' '''

    info = command_execute.execute(cmd, True)

    streams = info['streams'] if 'streams' in info else []

    return streams[0] if len(streams) > 0 else None

# 最简单的单线程版本（内存占用最小）
def clip_frame_simple(video_file, interval=1, timeline: list[int] = None, quality=60):
    """
    单线程版本，内存占用最小但速度较慢
    """
    import os
    import cv2
    import logging

    logger = logging.getLogger(__name__)

    out_put_dir = os.path.join(os.path.dirname(video_file), 'clip_frame')
    if not os.path.exists(out_put_dir):
        os.makedirs(out_put_dir)

    cap = cv2.VideoCapture(video_file)
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # 确定要截取的帧
    if timeline:
        frame_positions = [int(t * fps) for t in timeline if t * fps < frame_count]
    else:
        frame_positions = list(range(0, frame_count, int(fps) * interval))

    logger.info(f'视频文件:{video_file}, 帧数:{frame_count}, fps:{fps}, 截取帧数:{len(frame_positions)}')

    successful_count = 0
    for pos in frame_positions:
        cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
        ret, frame = cap.read()
        if ret:
            timestamp = int(pos / fps)
            filename = os.path.join(out_put_dir, f'{timestamp}_frame.jpg')

            success = cv2.imwrite(filename, frame, [cv2.IMWRITE_JPEG_QUALITY, quality])
            if success:
                successful_count += 1
                if successful_count % 100 == 0:  # 每100帧打印一次进度
                    logger.info(f'已处理 {successful_count} 帧')

    cap.release()
    logger.info(f'成功截取 {successful_count} 帧')
    return out_put_dir

def clip_frame_optimized(video_file, interval=1, timeline: list[int] = None, quality=60, max_workers=4):
    """
    内存优化版本的视频截帧函数

    Args:
        video_file: 视频文件路径
        interval: 截帧间隔（秒）
        timeline: 指定时间点列表（秒）
        quality: JPEG质量 (1-100，默认60)
        max_workers: 并行写入线程数
    """
    import os
    import cv2
    from concurrent.futures import ThreadPoolExecutor, as_completed
    import logging

    logger = logging.getLogger(__name__)

    out_put_dir = os.path.join(os.path.dirname(video_file), 'clip_frame')
    if not os.path.exists(out_put_dir):
        os.makedirs(out_put_dir)

    cap = cv2.VideoCapture(video_file)
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # 确定要截取的帧
    if timeline:
        frame_positions = [int(t * fps) for t in timeline if t * fps < frame_count]
    else:
        frame_positions = list(range(0, frame_count, int(fps) * interval))

    logger.info(f'视频文件:{video_file}, 帧数:{frame_count}, fps:{fps}, 截取帧数:{len(frame_positions)}')

    def process_frame(pos):
        """处理单个帧的函数"""
        # 每个线程使用独立的VideoCapture对象
        local_cap = cv2.VideoCapture(video_file)
        try:
            local_cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
            ret, frame = local_cap.read()
            if ret:
                timestamp = int(pos / fps)
                filename = os.path.join(out_put_dir, f'{timestamp}_frame.jpg')

                # 可选：调整图片大小减少内存占用
                # frame = cv2.resize(frame, None, fx=0.8, fy=0.8)

                # 直接写入文件，不在内存中累积
                success = cv2.imwrite(filename, frame, [cv2.IMWRITE_JPEG_QUALITY, quality])
                if success:
                    logger.info(f'视频文件:{video_file} ,截图:{filename}')
                    return filename
                else:
                    logger.error(f'写入失败: {filename}')
                    return None
        except Exception as e:
            logger.error(f'处理帧 {pos} 时出错: {e}')
            return None
        finally:
            local_cap.release()

    cap.release()  # 释放主VideoCapture对象

    # 使用线程池处理，但限制并发数量避免内存爆炸
    successful_files = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 使用as_completed来逐个处理完成的任务，避免内存积累
        future_to_pos = {executor.submit(process_frame, pos): pos for pos in frame_positions}

        for future in as_completed(future_to_pos):
            pos = future_to_pos[future]
            try:
                result = future.result()
                if result:
                    successful_files.append(result)
            except Exception as e:
                logger.error(f'线程处理帧 {pos} 时出错: {e}')

    logger.info(f'成功截取 {len(successful_files)} 帧')
    return out_put_dir


def clip_frame_optimized_2(video_file, interval=1, timeline: list[int] = None, quality=60, max_workers=6):
    """
    优化版本的视频截帧函数

    Args:
        video_file: 视频文件路径
        interval: 截帧间隔（秒）
        timeline: 指定时间点列表（秒）
        quality: JPEG质量 (1-100，默认60)
        max_workers: 并行写入线程数
    """
    out_put_dir = os.path.join(os.path.dirname(video_file), 'clip_frame')
    if not os.path.exists(out_put_dir):
        os.makedirs(out_put_dir)

    cap = cv2.VideoCapture(video_file)
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # 确定要截取的帧
    if timeline:
        frame_positions = [int(t * fps) for t in timeline if t * fps < frame_count]
    else:
        frame_positions = list(range(0, frame_count, int(fps) * interval))

    logger.info(f'视频文件:{video_file}, 帧数:{frame_count}, fps:{fps}, 截取帧数:{len(frame_positions)}')

    # 批量读取和处理
    frames_data = []


    # 多线程并行写入文件
    def write_frame(data):
        frame, filename, qual = data
        cv2.imwrite(filename, frame, [cv2.IMWRITE_JPEG_QUALITY, qual])

    for pos in frame_positions:
        cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
        ret, frame = cap.read()
        if ret:
            # 预先调整图片大小（可选）
            # frame = cv2.resize(frame, None, fx=0.8, fy=0.8)  # 缩小20%

            timestamp = int(pos / fps)
            filename = os.path.join(out_put_dir, f'{timestamp}_frame.jpg')
            logger.info(f'视频文件:{video_file} ,截图:{filename}')
            frames_data.append((frame.copy(), filename, quality))

        if len(frames_data) > 20:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                executor.map(write_frame, frames_data)
            frames_data = []


    cap.release()

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        executor.map(write_frame, frames_data)

    return out_put_dir