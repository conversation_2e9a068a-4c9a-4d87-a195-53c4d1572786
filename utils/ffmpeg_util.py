import logging
import os

from utils import command_execute

logger = logging.getLogger(__name__)
video_codec = os.environ.get('VIDEO_CODEC', 'libx264')


def default_compress(_input, output):
    cmd = f'''ffmpeg -i '{_input}' \
     -c:v {video_codec}  \
     -maxrate 5m  \
     -bufsize 10m  \
     -preset medium  \
     -pix_fmt yuv420p \
     -c:a aac \
     -y '{output}' '''

    command_execute.execute(cmd)

    return _check_output(output)


def low_quality_compress(_input, output):
    cmd = f'''ffmpeg -i '{_input}' \
    -c:v {video_codec} \
    -preset medium \
    -crf 25 \
    -pix_fmt yuv420p \
    -vf "scale='if(gt(iw\,ih)\,960\,-1)':'if(gt(iw\,ih)\,-1\,960)'" \
    -c:a aac \
    -y '{output}' '''

    command_execute.execute(cmd)

    return _check_output(output)


def quick_compress(_input, output):
    cmd = f'''ffmpeg -i '{_input}' \
    -vcodec {video_codec} \
    -preset ultrafast \
    -crf 35 -an \
    -y \
    '{output}' '''

    command_execute.execute(cmd)

    return _check_output(output)


def to_voice(_input, output):
    cmd = f'''ffmpeg -i '{_input}' -vn -acodec libmp3lame -ar 44100 -ac 2 '{output}' -y'''

    command_execute.execute(cmd)

    return _check_output(output)


def _check_output(output):
    if not os.path.exists(output):
        return False

    if os.path.getsize(output) == 0:
        return False

    logger.info(f"output文件检查结果:{output} size:{os.path.getsize(output)}")
    return True
