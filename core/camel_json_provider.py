import json

from flask.json.provider import <PERSON><PERSON>ultJSONProvider
from core.func import dict_keys_to_camel_case
from core.trace import get_trace_id


class CamelJSONProvider(DefaultJSONProvider):

    def dumps(self, obj, **kwargs):
        kwargs.setdefault("default", self.default)
        kwargs.setdefault("ensure_ascii", False)
        obj_dict = dict_keys_to_camel_case(obj)
        obj_dict['tid'] = get_trace_id()

        return json.dumps(obj_dict, **kwargs)


def deploy_json_provider(app):
    app.json_provider_class = CamelJSONProvider
    app.json = app.json_provider_class(app)
