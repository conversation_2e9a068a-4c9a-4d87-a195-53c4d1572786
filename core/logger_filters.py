import logging
import os
import sys

from core.constant import STR_EMPTY
from core.trace import get_trace_id


class TraceFilter(logging.Filter):
    """
    自定义日志过滤器，用于添加trace_id到日志记录中
    """

    def filter(self, record):
        trace_id = get_trace_id()
        if trace_id:
            record.trace_id = trace_id
        else:
            record.trace_id = "NO_TRACE"
        return True


REPLACE_PATH = sys.path


class PathnameFilter(logging.Filter):
    """
    自定义日志过滤器，用于添加文件路径到日志记录中
    """

    def filter(self, record):
        location = record.pathname.replace(os.environ['APP_ROOT'] + '/', STR_EMPTY)
        for p in REPLACE_PATH:
            location = location.replace(p + '/site-packages/', STR_EMPTY)

        record.location = location.replace('/', '.').replace('.py', STR_EMPTY)

        return True
