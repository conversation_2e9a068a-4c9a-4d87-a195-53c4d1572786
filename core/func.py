import inspect
from datetime import datetime

import arrow
import humps
from pydantic import BaseModel
from flask import g
from flask_jwt_extended import get_jwt


def dict_keys_to_camel_case(d):
    if isinstance(d, dict):
        d = humps.camelize(d)
        for k, v in d.items():
            d[k] = dict_keys_to_camel_case(v)
        return d
    if isinstance(d, list):
        return [dict_keys_to_camel_case(item) for item in d]

    if isinstance(d, int):
        if len(str(d)) > 8:
            return str(d)
    if isinstance(d, BaseModel):
        return dict_keys_to_camel_case(d.model_dump())

    if hasattr(d, 'to_dict') and inspect.ismethod(getattr(d, 'to_dict')):
        return dict_keys_to_camel_case(d.to_dict())

    if hasattr(d, '__dict__'):
        return dict_keys_to_camel_case(d.__dict__)

    if isinstance(d, datetime):
        return arrow.get(d).format("YYYY_MM_DD_HH_mm_ss")

    if isinstance(d, bytes):
        return d.decode('utf-8')

    return d


def is_vertical(width, height, rotate):
    if rotate in (0, 180):
        return width < height
    elif rotate in (90, 270):
        return height < width
    else:
        return None


def get_user_identity():
    # 优先检查是否是内部调用
    if hasattr(g, 'internal_user') and g.internal_user:
        return g.internal_user['cid'], g.internal_user['oid']

    # 否则从JWT中获取
    jwt = get_jwt()
    cid = jwt['cid']
    oid = jwt['oid'] if jwt.get('oid') else 0
    return cid, oid
