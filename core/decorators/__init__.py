import logging
from functools import wraps
from hashlib import md5

from flask import request, g
from flask_jwt_extended import jwt_required, get_jwt

from core.constant import LOCK_PREFIX, STR_EMPTY
from core.exception.ServiceException import ServiceException
from core.ext import db, redlock_ext

logger = logging.getLogger(__name__)


def transactional(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            db.session.commit()
            return result
        except Exception as e:
            db.session.rollback()
            raise

    return wrapper


def lock(keys:list=[], ttl=1000, retry_times=3, retry_delay=300):
    """
    分布式锁
    :return:
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            lock_prefix = LOCK_PREFIX
            lock_key = STR_EMPTY
            for key in keys:
                lock_key += f"{key}:{kwargs.get(key)}"

            lock_key = f'{lock_prefix}:{func.__name__}:{md5(lock_key.encode()).hexdigest()}'

            r_lock = redlock_ext.get_lock(lock_key, ttl=ttl, retry_times=retry_times, retry_delay=retry_delay)
            try:
                if r_lock:
                    result = func(*args, **kwargs)
                    return result
                else:
                    raise ServiceException(f"拿锁失败：{func.__name__}")
            finally:
                if r_lock:
                    r_lock.release()

        return wrapper

    return decorator


def auth_required(allow_internal=True):
    """
    认证装饰器,支持JWT认证和内部调用
    :param allow_internal: 是否允许内部调用（通过请求头传递用户信息）
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 检查是否是内部调用
            if allow_internal:
                internal_cid = request.headers.get('cid')
                internal_oid = request.headers.get('OID')

                if internal_cid is not None:
                    # 内部调用，直接设置用户身份信息
                    g.internal_user = {
                        'cid': int(internal_cid),
                        'oid': int(internal_oid) if internal_oid else 0
                    }
                    return func(*args, **kwargs)

            # 如果不是内部调用，使用JWT认证
            return jwt_required()(func)(*args, **kwargs)

        return wrapper
    return decorator
