import logging
from typing import Optional

from flask import Flask
from pymilvus import MilvusClient

logger = logging.getLogger(__name__)


class MilvusExt:

    def __init__(self):
        self.app = None
        self.client: Optional[MilvusClient] = None

    def init_app(self, app: Flask):
        if 'milvus' in app.extensions:
            return
        logger.info('milvus init')

        logger.info(f'''host:{app.config.get('MILVUS_URI')},user:{app.config.get('MILVUS_DB_NAME')}''')

        self.app = app
        self.client = MilvusClient(
            uri=app.config.get('MILVUS_URI'),
            user=app.config.get('MILVUS_USER'),
            password=app.config.get('MILVUS_PASSWORD'),
            db_name=app.config.get('MILVUS_DB_NAME')
        )
        logger.info('milvus init success')

        app.extensions['milvus'] = self
