from typing import Optional

from celery import Celery, Task
from flask import Flask

import logging

from core.trace import get_trace_id, get_new_trace_id

logger = logging.getLogger(__name__)


class CeleryExt:

    def __init__(self):
        self.celery: Optional[Celery] = None

    def init_app(self, app: Flask):
        class FlaskTask(Task):

            def __call__(self, *args: object, **kwargs: object) -> object:
                with app.app_context():
                    logger.info(f"celery task {self.name} start  {get_new_trace_id()}")
                    return self.run(*args, **kwargs)

        celery = Celery(__name__, task_cls=FlaskTask)

        # 配置Celery
        celery.conf.update(app.config)

        app.extensions['celery'] = celery


celery_ext = CeleryExt()
