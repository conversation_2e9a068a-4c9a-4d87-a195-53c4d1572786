import threading
import time

from flask import Flask

"""
雪花id服务
"""


class SnowflakeIdExt:
    app_instance_id = 1024 % 65535

    last_time = int(time.time())

    START_TIME_STAMP = 1640620800

    TIME_STAMP_LEFT_SHIFT = 32

    SEQUENCE_BITS = 16

    seq = 0

    _lock = threading.Lock()

    def init_app(self, flask_app: Flask):
        with flask_app.app_context():
            from core.ext import redis_client
            self.app_instance_id = redis_client.incr("app_ip_yk:snowflakeId:instance")

    def next_id(self):
        with self._lock:
            timestamp = int(time.time())

            if timestamp == self.last_time:
                self.seq = (self.seq + 1) % 65535
                if self.seq == 0:
                    timestamp = self.last_time + 1
            else:
                self.seq = 0

            self.last_time = timestamp

            return ((timestamp - self.START_TIME_STAMP) << self.TIME_STAMP_LEFT_SHIFT) | (
                    self.seq << self.SEQUENCE_BITS) | self.app_instance_id
