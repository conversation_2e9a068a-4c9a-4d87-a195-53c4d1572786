import base64
import logging
import os

from cryptography.hazmat.primitives.ciphers.aead import AESGCM

logger = logging.getLogger(__name__)


class GCMEncryptDecrypterExt:
    _DEFAULT_SECRET_KEY_PATH_LIST = ['/app/secret_key.b64', '/tmp/secret_key.b64']

    def __init__(self, secret_key_path=None):
        # 密钥路径
        self._secret_key_path = secret_key_path
        # 加载密钥
        self._secret_key = None
        # 创建AES-GCM对象
        self._aes_gcm = None

    def init_app(self, app):
        app.extensions['gcm_encrypt_decrypter'] = self

        if f'''{os.environ['APP_ROOT']}/secret_key.b64''' not in self._DEFAULT_SECRET_KEY_PATH_LIST:
            self._DEFAULT_SECRET_KEY_PATH_LIST.append(f'''{os.environ['APP_ROOT']}/secret_key.b64''')

        if os.path.expanduser('~/secret_key.b64') not in self._DEFAULT_SECRET_KEY_PATH_LIST:
            self._DEFAULT_SECRET_KEY_PATH_LIST.append(os.path.expanduser('~/secret_key.b64'))

        # 加载密钥
        self._secret_key = self._load_secret_key()

        # 创建AES-GCM对象
        self._aes_gcm = AESGCM(self._secret_key)

    def decrypt(self, encode_str):
        cipher_data = base64.b64decode(encode_str)

        iv = cipher_data[:12]

        ciphertext = cipher_data[12:-16]

        tag = cipher_data[-16:]

        return self._aes_gcm.decrypt(iv, ciphertext + tag, associated_data=None).decode('utf-8')

    def encrypt(self, plaintext: str) -> str:
        iv = os.urandom(12)

        ciphertext = self._aes_gcm.encrypt(iv, plaintext.encode('utf-8'), associated_data=None)

        encrypted_data = iv + ciphertext

        encrypted_b64 = base64.b64encode(encrypted_data).decode('utf-8')

        return encrypted_b64

    def _load_secret_key(self):
        for secret_key_path in self._DEFAULT_SECRET_KEY_PATH_LIST:
            logger.info(f'尝试加载密钥: {secret_key_path}')
            if os.path.exists(secret_key_path):
                self._secret_key_path = secret_key_path
                break

        with open(self._secret_key_path) as f:
            secret_key = f.read().strip()
            if secret_key is None or len(secret_key) == 0:
                raise Exception('secret_key is None')

            return base64.b64decode(secret_key)
