import logging

from flask_apscheduler import APScheduler
from flask_jwt_extended import JWTManager
from flask_sqlalchemy import SQLAlchemy
from flask_redis import FlaskRedis

from core.ext.celery_ext import celery_ext
from core.ext.gcm_encrypt_decrypter_ext import GCMEncryptDecrypterExt
from core.ext.milvus_ext import MilvusExt
from core.ext.snowflake_id_ext import SnowflakeIdExt
from core.ext.redlock_ext import RedlockExt
from core.ext.elasticsearch_ext import ElasticsearchExt
from core.ext.oss_ext import OssExt
from core.ext.volcengine_ext import VolcengineExt

# 创建 SQLAlchemy 实例
db = SQLAlchemy()

# jwt
jwt = JWTManager()

# 创建 Redis 实例
redis_client = FlaskRedis()

#定时任务扩展
scheduler = APScheduler()

# 雪花id
snowflake_ext = SnowflakeIdExt()

# Redlock 分布式锁
redlock_ext = RedlockExt()

# Elasticsearch 扩展
elasticsearch_ext = ElasticsearchExt()

# 阿里云oss
oss_ext = OssExt()

# milvus
milvus_ext = MilvusExt()

# 火山云
volcengine_ext = VolcengineExt()

# 加密工具
encrypt_decrypter = GCMEncryptDecrypterExt()

logger = logging.getLogger()


def init_ext(app):
    with app.app_context():
        # 初始化数据库
        app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
            'pool_size': 20,
            'max_overflow': 20,
            'pool_timeout': 30,
        }
        db.init_app(app)

        # 初始化扩展
        jwt.init_app(app)

        # 初始化 Redis
        redis_client.init_app(app)

        # 初始化雪花ID
        snowflake_ext.init_app(app)

        # 初始化 Redlock
        redlock_ext.init_app(app)

        # 初始化 Elasticsearch
        elasticsearch_ext.init_app(app)

        # 初始化 阿里云oss
        oss_ext.init_app(app)

        # 初始化 milvus
        milvus_ext.init_app(app)

        # 初始化 火山云
        volcengine_ext.init_app(app)

        # 初始化定时任务
        scheduler.init_app(app)

        # 初始化 celery
        celery_ext.init_app(app)
