import warnings

from elasticsearch import Elasticsearch
import logging

from elasticsearch.exceptions import ElasticsearchWarning

from core.exception.ServiceException import ServiceException

logger = logging.getLogger(__name__)


class ElasticsearchExt:
    def __init__(self):
        """
        初始化Elasticsearch客户端
        :param hosts: Elasticsearch主机列表
        :param kwargs: 其他Elasticsearch连接参数
        """
        self.client: Elasticsearch | None = None
        self.app = None

    def init_app(self, app):
        """
        初始化Flask应用
        :param app: Flask应用实例
        """
        self.app = app
        warnings.filterwarnings('ignore', category=ElasticsearchWarning)

        # 从配置中获取Elasticsearch配置
        hosts = [app.config.get('ELASTICSEARCH_HOSTS')]
        if len(hosts) == 0:
            raise ServiceException('Elasticsearch hosts is empty', 500)

        # 其他ES配置参数
        es_kwargs = app.config.get('ELASTICSEARCH_KWARGS', {})

        # 初始化Elasticsearch客户端
        try:
            self.client = Elasticsearch(
                hosts=hosts,
                basic_auth=app.config.get('ELASTICSEARCH_BASIC_AUTH', ()),
                **es_kwargs,
            )
            # 验证连接
            if self.client.ping():
                logger.info(f"Successfully connected to Elasticsearch at {hosts}")
            else:
                logger.warning(f"Connected to Elasticsearch at {hosts} but ping failed")
        except Exception as e:
            logger.error(f"Failed to connect to Elasticsearch: {str(e)}")
            self.client = None
            raise e
