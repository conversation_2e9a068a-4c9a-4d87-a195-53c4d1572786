import logging
import os

import oss2
from flask import Flask

from core.constant import STR_EMPTY
from core.exception.ServiceException import ServiceException
from urllib import parse

from utils import url_util

logger = logging.getLogger(__name__)


class OssExt:

    def __init__(self):
        self.app: Flask | None = None

        self.client: oss2.Bucket | None = None

        self.domain: str = STR_EMPTY

    def init_app(self, app: Flask):
        self.app = app
        self.app.extensions['oss'] = self

        auth = oss2.Auth(app.config.get('OSS_ACCESS_KEY_ID'), app.config.get('OSS_ACCESS_KEY_SECRET'))

        bucket = oss2.Bucket(
            auth=auth,
            endpoint=app.config.get('OSS_ENDPOINT'),
            bucket_name=app.config.get('OSS_BUCKET_NAME')
        )

        self.client = bucket
        endpoint = parse.urlparse(self.client.endpoint)
        self.domain = f'{endpoint.scheme}://{self.client.bucket_name}.{endpoint.netloc}'

    def add_domain(self, url: str, sign=False) -> str:
        url = os.path.join(self.domain.strip('/'), url.strip('/'))
        return self.sign_url(url) if sign else url

    def download(self, url, filename=None, dirname=None):
        if filename is None:
            if dirname is not None:
                filename = os.path.join(dirname, os.path.basename(url))
            else:
                raise ServiceException('filename ,dirname 不能同时为空')

        logger.info(f'开始下载文件 {url} -> {filename}')

        self.client.get_object_to_file(
            key=url_util.get_key(url),
            filename=filename
        )
        return filename

    def sign_url(self, url, expires=3600):
        if not url:
            return url
        return self.client.sign_url('GET', url_util.get_key(url), expires)

    @staticmethod
    def replace_domain(url: str) -> str:
        if not url:
            return url
        return f'''https://yk-ai-video.oss-cn-heyuan.aliyuncs.com/{url_util.get_key(url)}'''
