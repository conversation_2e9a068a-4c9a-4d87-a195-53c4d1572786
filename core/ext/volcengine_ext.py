from typing import Optional

from flask import Flask

from sdk.volcengine.voice_ata import VoiceAta


class VolcengineExt:

    def __init__(self):
        self.voice_ata: Optional[VoiceAta] = None

    def init_app(self, app: Flask):
        app_id = app.config.get('VOLCENGINE_APP_ID', '9047990488')
        app_key = app.config.get('VOLCENGINE_APP_KEY', 'LHreNNwowg6YMdd5FH-Ibw7brPlIVfYA')

        self.voice_ata = VoiceAta(app_id, app_key)

        app.extensions['volcengine_ext'] = self
