from anytree import Node
from sqlalchemy import select

from core.ext import db
from repositories.mysql.model.material_folder import MaterialFolder


class FolderExt:

    def __init__(self):
        self.node_dict: dict = {}
        self.folder_list: list[MaterialFolder] = []

    def init_app(self, app):
        with app.app_context():
            query = select(MaterialFolder).filter(MaterialFolder.deleted == 0)
            self.folder_list = db.session.execute(query)
        for folder in self.folder_list:
            self.node_dict[folder.id] = Node(folder.id)
        for folder in self.folder_list:
            if folder.pid == 0:
                continue
            self.node_dict[folder.id].parent = self.node_dict[folder.pid]
