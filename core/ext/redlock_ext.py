import logging

from redlock import RedLock

logger = logging.getLogger(__name__)


class RedlockExt:
    redis_servers: list[dict] | None = None
    redis_server_url: str | None = None

    def init_app(self, app):
        """初始化 Redlock 扩展"""
        if app.config.get('REDIS_URL') is None:
            raise Exception('REDIS_URL is not set')
        logger.info(f'Redis lock URL: {app.config.get("REDIS_URL")}')
        self.redis_server_url = app.config.get('REDIS_URL')

    def get_lock(self, resource, ttl=10000, retry_times=3, retry_delay=300):
        logger.info(f'get_locker: {resource}')
        rlock = RedLock(
            resource=resource,
            connection_details=[{'url': self.redis_server_url}],
            ttl=ttl,
            retry_times=retry_times,
            retry_delay=retry_delay
        )
        if rlock.acquire():
            return rlock
        else:
            logger.info(f'get_locker_error: {resource}')
            return None


