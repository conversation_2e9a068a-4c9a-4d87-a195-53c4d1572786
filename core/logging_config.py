import json
import os
import logging
import logging.config

# 日志格式
LOG_FORMAT = '%(asctime)s %(levelname)s [%(trace_id)s] %(location)s:%(lineno)d %(message)s'
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'


def setup_logging(name='server'):
    log_dir = os.path.join(os.environ["APP_ROOT"], "logs")

    logging.info(f"setup_logging dir {log_dir}")

    os.makedirs(log_dir, exist_ok=True)

    # 加载 JSON 文件
    with open(os.path.join(os.environ["APP_ROOT"], "logging_config.json"), "r", encoding="utf-8") as f:
        config = json.load(f)

    # 动态替换日志文件路径
    config["handlers"]["file"]["filename"] = os.path.join(log_dir, f"{name}.log")

    logging.config.dictConfig(config)
