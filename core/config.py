import json
import logging
import os
import re
from urllib.parse import quote

import requests
import yaml
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from flask import Flask
from nacos.client import NacosClient

from core.ext import encrypt_decrypter

secret_value_pattern = re.compile(r'YK_ENC\(([^)]+)\)')

logger = logging.getLogger(__name__)


def special_configuration(app):
    if app.config.get('SCHEDULER_JOBSTORES'):
        app.config['SCHEDULER_JOBSTORES'] = {
            'default': SQLAlchemyJobStore(url=app.config.get('SCHEDULER_JOBSTORES'))
        }
        return


def load_config(app: Flask):
    nacos_server = os.environ.get('NACOS_SERVER')
    nacos_namespace = os.environ.get('NACOS_NAMESPACE')
    nacos_data_id = os.environ.get('NACOS_DATA_ID', 'material_ai_yk.yaml')

    print(f'nacos:{nacos_server} namespace:{nacos_namespace} data_id:{nacos_data_id}')
    client = NacosClient(
        server_addresses=nacos_server,
        namespace=nacos_namespace,
    )
    config_yaml = client.get_config(data_id=nacos_data_id, group='DEFAULT_GROUP')

    config_dict = yaml.safe_load(config_yaml)

    load_ali_ak(encrypt_decrypter)

    decode_key(config_dict, encrypt_decrypter)
    # 覆盖本地.env中已存在的配置
    for (k, v) in config_dict.items():
        if k in os.environ:
            config_dict[k] = os.environ.get(k)

    # 加载配置
    app.config.update(config_dict)
    for (k, v) in config_dict.items():
        os.environ[k] = str(v)

    # specialConfiguration
    # special_configuration(app)
    # print(config_dict)
    # exit()

    return config_dict


def decode_key(config_dict, encrypt_decrypter):
    for (k, v) in config_dict.items():
        v_str = json.dumps(v)
        matches = secret_value_pattern.findall(v_str)
        if matches:
            for secret_value in matches:
                # 不需要移除 << >> 标记，直接使用匹配到的内容
                full_match = f'YK_ENC({secret_value})'
                try:
                    plain = encrypt_decrypter.decrypt(secret_value.encode())
                    if '://' in v_str:
                        plain = quote(plain)
                    v_str = v_str.replace(full_match, plain)
                except Exception as _:
                    logger.info(f"解密失败：{full_match}")
            config_dict[k] = json.loads(v_str)


def load_ali_ak(encrypt_decrypter):
    response = requests.get(
        'https://api.inngke.com/api/secret-conf/config?appId=5&configKeys=oss.secret_key,oss.secret_id',
        headers={'app-id': 'auth_ip_yk'})
    res = response.json()
    data = res.get('data', {})

    config_map = {item.get('configKey'): item.get('configValue') for item in data}

    os.environ['OSS_ACCESS_KEY_ID'] = encrypt_decrypter.decrypt(config_map.get('oss.secret_id'))
    os.environ['OSS_ACCESS_KEY_SECRET'] = encrypt_decrypter.decrypt(config_map.get('oss.secret_key'))
