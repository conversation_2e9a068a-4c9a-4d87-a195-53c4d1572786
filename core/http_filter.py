import json
import logging
import traceback
from flask import request
from marshmallow import ValidationError
from werkzeug.exceptions import UnprocessableEntity
from webargs.flaskparser import parser

from core.constant import STR_EMPTY
from core.exception.ServiceException import ServiceException

logger = logging.getLogger(__name__)


def error_log_handler(error):
    message = '服务器错误'
    code = 500

    if isinstance(error, ServiceException):
        message = error.message
        code = error.code if hasattr(error, 'code') else 500
    elif isinstance(error, ValidationError):
        # 处理参数验证错误
        msg_list = []
        i = 0
        for msg_key in ['querystring', 'json']:
            for field, msgs in error.messages.get(msg_key).items() if msg_key in error.messages else []:
                if isinstance(msgs,dict):
                    msg_list.append(f'''「{field}: {','.join([ v[0] for key,v in msgs.items()])}」''')
                else:
                    msg_list.append(f'''「{field}: {','.join(msgs)}」''')
                i += 1
        message = '参数校验失败:'
        message += ' '.join(msg_list)
    else:
        logger.error(traceback.format_exc())

    return {
        'code': code,
        'msg': message,
        'data': None
    }


# webargs 自定义错误处理器
@parser.error_handler
def handle_webargs_error(error, req, schema, *, error_status_code, error_headers):
    """
    webargs 参数校验错误的自定义处理器
    这个处理器会在 use_args 装饰器校验失败时被调用
    """
    logger.warning(f"参数校验失败: {error.messages}")

    # 创建一个带有特殊标识的 UnprocessableEntity 异常
    # 这样我们就能在 error_log_handler 中区分是否为参数校验错误
    exc = UnprocessableEntity()
    exc.data = {
        'messages': error.messages,
        'is_validation_error': True  # 添加标识
    }

    # 抛出异常，让 Flask 的错误处理器处理
    raise error


def log_before_request():
    params = request.args
    paramsStr = '&'.join(f'{k}={v}' for k, v in params.items())
    bodyStr = STR_EMPTY
    if request.content_length and request.content_length > 0:
        bodyStr = json.dumps(request.json, ensure_ascii=False)
    headersStr = ';'.join(f'{k}:{v}' for k, v in request.headers.items())
    logger.info(f'{request.method} {request.url} query:{paramsStr} body:{bodyStr} headers:{headersStr}')
