import threading
import uuid

from core.constant import STR_EMPTY

tread_local = threading.local()


def get_trace_id(current_tid=STR_EMPTY):
    """获取当前上下文的trace_id"""
    if 'trace_id' not in tread_local.__dict__:
        tread_local.trace_id = str(uuid.uuid4())[:8]
    if current_tid:
        tread_local.trace_id = current_tid + '-' + tread_local.trace_id

    return tread_local.trace_id


def get_new_trace_id():
    tread_local.trace_id = str(uuid.uuid4())[:8]
    return tread_local.trace_id
