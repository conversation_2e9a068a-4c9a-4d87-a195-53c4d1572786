import logging
import os

from dotenv import load_dotenv

from flask import Flask

from scheduler_task import register_scheduler
from api import register_blueprint
from cli import register_command
from core.camel_json_provider import deploy_json_provider
from core.config import load_config
from core.http_filter import log_before_request, error_log_handler
from core.logging_config import setup_logging
from core.ext import init_ext, encrypt_decrypter
from utils import env_util

logger = logging.getLogger(__name__)

os.environ['APP_ROOT'] = os.path.abspath(os.path.dirname(__file__) + '/..')

load_dotenv(f'''{os.environ['APP_ROOT']}/.env''')


def create_app(is_celery=False):
    app = Flask(__name__)

    # 先初始化加密解密器
    encrypt_decrypter.init_app(app)

    load_config(app)

    setup_logging()

    with app.app_context():
        if not is_celery and env_util.is_dev():
            from flask_cors import CORS
            CORS(app)

    # 初始化扩展
    init_ext(app)

    # 配置json解析器
    deploy_json_provider(app)

    # 注册蓝图
    register_blueprint(app)

    # 注册命令行
    register_command(app)

    # 日志拦截器
    app.before_request_funcs = {None: [log_before_request]}

    # 统一错误处理
    app.register_error_handler(Exception, error_log_handler)

    if not is_celery:
        from tasks import register_task
        register_task(app)
        
    # 注册并启动定时任务
    register_scheduler()

    return app if not is_celery else app.extensions['celery']
